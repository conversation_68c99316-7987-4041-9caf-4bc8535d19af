#!/usr/bin/env python3
"""
ADX过滤器快速测试脚本
用于快速验证ADX过滤功能和效果
"""

import os
import sys
from backtest_after_filter import BacktestFilter

def quick_adx_test():
    """快速ADX过滤测试"""
    print("🚀 ADX过滤器快速测试")
    print("="*50)
    
    # 检查必要文件
    csv_file = "backtest_money_log_quick.csv"
    db_file = "coin_data.db"
    
    if not os.path.exists(csv_file):
        print(f"❌ 找不到回测结果文件: {csv_file}")
        print("请先运行回测生成结果文件")
        return False
    
    if not os.path.exists(db_file):
        print(f"❌ 找不到数据库文件: {db_file}")
        print("请确保数据库文件存在")
        return False
    
    try:
        # 创建过滤器
        print("📊 加载数据...")
        filter_engine = BacktestFilter(csv_file, db_file)
        
        # 加载价格数据
        filter_engine.load_price_volume_data('ETH', '5m', 'spot')
        
        original_count = len(filter_engine.original_df)
        print(f"原始交易信号数量: {original_count}")
        
        # 测试不同ADX配置
        test_configs = [
            {"name": "宽松", "min_adx": 20.0, "max_adx": 75.0},
            {"name": "标准", "min_adx": 25.0, "max_adx": 70.0},
            {"name": "保守", "min_adx": 30.0, "max_adx": 65.0},
        ]
        
        print(f"\n📈 ADX过滤测试结果:")
        print(f"{'配置':<8} {'通过率':<10} {'胜率':<10} {'平均收益':<12} {'总收益':<12}")
        print("-" * 60)
        
        best_config = None
        best_score = -float('inf')
        
        for config in test_configs:
            # 重置原始数据
            filter_engine.original_df = filter_engine.original_df.copy()
            
            # 应用ADX过滤
            filtered_df = filter_engine.apply_adx_filter(
                adx_period=14,
                min_adx=config["min_adx"],
                max_adx=config["max_adx"]
            )
            
            if len(filtered_df) > 0:
                pass_rate = len(filtered_df) / original_count * 100
                win_rate = len(filtered_df[filtered_df['ProfitLoss'] > 0]) / len(filtered_df) * 100
                avg_profit = filtered_df['ProfitLoss'].mean()
                total_profit = filtered_df['ProfitLoss'].sum()
                
                # 计算综合得分 (胜率 * 总收益 / 信号数量)
                score = win_rate * total_profit / len(filtered_df) if len(filtered_df) > 0 else 0
                
                if score > best_score:
                    best_score = score
                    best_config = config.copy()
                    best_config.update({
                        'pass_rate': pass_rate,
                        'win_rate': win_rate,
                        'avg_profit': avg_profit,
                        'total_profit': total_profit,
                        'signal_count': len(filtered_df)
                    })
                
                print(f"{config['name']:<8} {pass_rate:>7.1f}%   {win_rate:>7.1f}%   "
                      f"{avg_profit:>9.2f}     {total_profit:>9.2f}")
            else:
                print(f"{config['name']:<8} {'0.0%':>9} {'N/A':>9} {'N/A':>11} {'N/A':>11}")
        
        # 显示最佳配置
        if best_config:
            print(f"\n🏆 推荐配置: {best_config['name']}")
            print(f"   ADX范围: {best_config['min_adx']:.1f} - {best_config['max_adx']:.1f}")
            print(f"   信号数量: {best_config['signal_count']} (通过率: {best_config['pass_rate']:.1f}%)")
            print(f"   胜率: {best_config['win_rate']:.1f}%")
            print(f"   总收益: {best_config['total_profit']:.2f}")
            
            # 生成推荐配置文件
            recommended_config = {
                "coin": "ETH",
                "interval": "5m",
                "market": "spot",
                "initial_capital": 1000,
                "adx_filter": {
                    "enabled": True,
                    "adx_period": 14,
                    "min_adx": best_config['min_adx'],
                    "max_adx": best_config['max_adx']
                }
            }
            
            import json
            with open('recommended_adx_config.json', 'w', encoding='utf-8') as f:
                json.dump(recommended_config, f, indent=2, ensure_ascii=False)
            
            print(f"💾 推荐配置已保存: recommended_adx_config.json")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def show_adx_analysis():
    """显示ADX数据分析"""
    print("\n📊 ADX市场分析")
    print("="*50)
    
    try:
        filter_engine = BacktestFilter("backtest_money_log_quick.csv", "coin_data.db")
        filter_engine.load_price_volume_data('ETH', '5m', 'spot')
        
        adx_data = filter_engine.calculate_adx_indicators(14)
        if adx_data is not None:
            print("ADX统计摘要:")
            print(f"平均ADX: {adx_data['adx'].mean():.2f}")
            print(f"ADX中位数: {adx_data['adx'].median():.2f}")
            print(f"ADX标准差: {adx_data['adx'].std():.2f}")
            
            # 分析不同ADX范围的分布
            ranges = [
                ("震荡市场", 0, 25),
                ("弱趋势", 25, 40),
                ("中等趋势", 40, 60),
                ("强趋势", 60, 100)
            ]
            
            print(f"\nADX分布:")
            for name, min_val, max_val in ranges:
                mask = (adx_data['adx'] >= min_val) & (adx_data['adx'] < max_val)
                count = mask.sum()
                percentage = count / len(adx_data) * 100
                print(f"{name}: {percentage:.1f}% ({count:,} 数据点)")
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")

if __name__ == '__main__':
    print("🔍 ADX过滤器快速测试工具")
    print("="*60)
    
    # 运行快速测试
    success = quick_adx_test()
    
    if success:
        # 显示ADX分析
        show_adx_analysis()
        
        print(f"\n✅ 测试完成！")
        print(f"\n💡 使用建议:")
        print(f"1. 使用推荐配置运行完整过滤:")
        print(f"   python backtest_after_filter.py --config recommended_adx_config.json")
        print(f"2. 查看详细使用指南:")
        print(f"   cat ADX_FILTER_USAGE_GUIDE.md")
        print(f"3. 运行完整示例:")
        print(f"   python example_adx_filter_usage.py")
    else:
        print(f"\n❌ 测试失败，请检查数据文件是否存在")
        sys.exit(1)