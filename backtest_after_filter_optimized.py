# backtest_after_filter_optimized.py
# 优化版本的回测过滤器，显著提升性能
# 主要优化：向量化操作替代循环，批量数据处理，缓存机制

import pandas as pd
import numpy as np
import argparse
import os
from datetime import datetime
import sqlite3
from typing import Dict, List, Optional, Tuple
import json
import bisect

# 引入分析工具
try:
    from analyze_money import analyze_and_plot_results
    from get_coin_history import get_table_name
except ImportError:
    print("警告: 无法导入 analyze_money 或 get_coin_history，部分功能可能受限")
    def analyze_and_plot_results(df, basename): 
        print(f"分析结果并绘图: {basename}")
    def get_table_name(coin, interval, market):
        return f"{coin.upper()}USDT_{interval.replace('m', 'min')}_{market}"

class OptimizedBacktestFilter:
    """优化版回测结果过滤器"""
    
    def __init__(self, csv_file: str, db_path: str = "coin_data.db"):
        self.csv_file = csv_file
        self.db_path = db_path
        self.original_df = None
        self.filtered_df = None
        self.price_data = None
        self.volume_data = None
        
        # 缓存机制
        self._signal_cache = {}
        
        # 加载原始回测结果
        self.load_original_results()
        
    def load_original_results(self):
        """加载原始回测结果"""
        if not os.path.exists(self.csv_file):
            raise FileNotFoundError(f"回测结果文件不存在: {self.csv_file}")
        
        self.original_df = pd.read_csv(self.csv_file)
        print(f"✅ 已加载原始回测结果: {len(self.original_df)} 条记录")
        
        # 优化：批量转换时间列
        self.original_df['StartTimestamp'] = pd.to_datetime(
            self.original_df['StartTimestamp'].str.replace(' UTC', ''), utc=True
        )
        self.original_df['EndTimestamp'] = pd.to_datetime(
            self.original_df['EndTimestamp'].str.replace(' UTC', ''), utc=True
        )
        
    def load_price_volume_data(self, coin: str, interval: str, market: str = "spot"):
        """加载价格和成交量数据用于过滤"""
        symbol = f"{coin.upper()}USDT"
        table_name = get_table_name(symbol, interval, market)
        
        try:
            conn = sqlite3.connect(self.db_path)
            query = f"""
            SELECT timestamp, open, high, low, close, volume 
            FROM {table_name} 
            ORDER BY timestamp ASC
            """
            df = pd.read_sql_query(query, conn)
            conn.close()
            
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='s', utc=True)
            df.set_index('timestamp', inplace=True)
            
            self.price_data = df[['open', 'high', 'low', 'close']].astype(float)
            self.volume_data = df['volume'].astype(float)
            
            print(f"✅ 已加载价格数据: {len(self.price_data)} 条记录")
            
        except Exception as e:
            print(f"❌ 加载价格数据失败: {e}")
            print(f"尝试的表名: {table_name}")
            self.price_data = None
            self.volume_data = None
    
    def calculate_adx_indicators_vectorized(self, adx_period: int = 14) -> pd.DataFrame:
        """向量化计算ADX指标 - 性能优化版本"""
        if self.price_data is None:
            return None
            
        df = self.price_data.copy()
        
        # 向量化计算True Range
        df['h_l'] = df['high'] - df['low']
        df['h_c'] = (df['high'] - df['close'].shift(1)).abs()
        df['l_c'] = (df['low'] - df['close'].shift(1)).abs()
        df['tr'] = df[['h_l', 'h_c', 'l_c']].max(axis=1)
        
        # 向量化计算方向移动
        df['up_move'] = df['high'] - df['high'].shift(1)
        df['down_move'] = df['low'].shift(1) - df['low']
        
        # 向量化计算+DM和-DM
        df['plus_dm'] = np.where(
            (df['up_move'] > df['down_move']) & (df['up_move'] > 0), 
            df['up_move'], 0
        )
        df['minus_dm'] = np.where(
            (df['down_move'] > df['up_move']) & (df['down_move'] > 0), 
            df['down_move'], 0
        )
        
        # 使用pandas的ewm进行平滑计算
        alpha = 1.0 / adx_period
        df['atr'] = df['tr'].ewm(alpha=alpha, adjust=False).mean()
        df['plus_di_raw'] = df['plus_dm'].ewm(alpha=alpha, adjust=False).mean()
        df['minus_di_raw'] = df['minus_dm'].ewm(alpha=alpha, adjust=False).mean()
        
        # 向量化计算+DI和-DI
        df['plus_di'] = 100 * df['plus_di_raw'] / df['atr']
        df['minus_di'] = 100 * df['minus_di_raw'] / df['atr']
        
        # 向量化计算DX和ADX
        df['dx'] = 100 * (df['plus_di'] - df['minus_di']).abs() / (df['plus_di'] + df['minus_di'])
        df['adx'] = df['dx'].ewm(alpha=alpha, adjust=False).mean()
        
        # 返回清理后的DataFrame
        return df[['adx', 'plus_di', 'minus_di', 'dx']].copy()
    
    def get_signals_vectorized(self, timestamps: pd.Series, signal_data: pd.DataFrame, 
                              signal_columns: List[str]) -> pd.DataFrame:
        """向量化获取信号值 - 性能优化版本"""
        if signal_data is None or signal_data.empty:
            return pd.DataFrame({col: np.nan for col in signal_columns}, index=timestamps.index)
        
        # 确保时区一致性
        signal_index = signal_data.index
        if hasattr(timestamps.iloc[0], 'tz') and hasattr(signal_index[0], 'tz'):
            if timestamps.iloc[0].tz != signal_index[0].tz:
                # 统一转换为UTC
                if timestamps.iloc[0].tz is None:
                    timestamps = pd.to_datetime(timestamps, utc=True)
                if signal_index[0].tz is None:
                    signal_index = pd.to_datetime(signal_index, utc=True)
                    signal_data = signal_data.copy()
                    signal_data.index = signal_index
        
        result_df = pd.DataFrame(index=timestamps.index)
        
        for col in signal_columns:
            # 为每个时间戳找到最近的信号值
            try:
                positions = signal_index.searchsorted(timestamps, side='right') - 1
                positions = np.clip(positions, 0, len(signal_data) - 1)
                
                # 只保留有效的位置（时间戳不早于信号数据）
                valid_mask = timestamps.values >= signal_index[0]
                
                values = np.full(len(timestamps), np.nan)
                values[valid_mask] = signal_data[col].iloc[positions[valid_mask]].values
                
                result_df[col] = values
            except Exception as e:
                print(f"⚠️  向量化查找失败，使用备用方法: {e}")
                # 备用方法：逐个查找
                values = []
                for ts in timestamps:
                    try:
                        valid_data = signal_data[signal_data.index <= ts]
                        if not valid_data.empty:
                            values.append(valid_data[col].iloc[-1])
                        else:
                            values.append(np.nan)
                    except:
                        values.append(np.nan)
                result_df[col] = values
        
        return result_df
    
    def apply_adx_filter_vectorized(self, adx_period: int = 14, min_adx: float = 25.0, 
                                   max_adx: float = 70.0) -> pd.DataFrame:
        """向量化ADX过滤 - 性能优化版本"""
        print(f"🔍 应用ADX趋势强度过滤 (ADX范围: {min_adx:.1f} - {max_adx:.1f})")
        
        if self.price_data is None:
            print("❌ 价格数据未加载，无法计算ADX，跳过ADX过滤")
            return self.original_df.copy()
        
        # 计算ADX指标
        adx_data = self.calculate_adx_indicators_vectorized(adx_period)
        if adx_data is None:
            print("❌ 无法计算ADX数据，跳过ADX过滤")
            return self.original_df.copy()
        
        # 向量化获取信号
        signal_columns = ['adx', 'plus_di', 'minus_di']
        signals = self.get_signals_vectorized(
            self.original_df['StartTimestamp'], 
            adx_data, 
            signal_columns
        )
        
        # 创建结果DataFrame
        filtered_df = self.original_df.copy()
        filtered_df['ADX'] = signals['adx']
        filtered_df['Plus_DI'] = signals['plus_di']
        filtered_df['Minus_DI'] = signals['minus_di']
        
        # 向量化过滤条件
        adx_valid = ~filtered_df['ADX'].isna()
        adx_in_range = (filtered_df['ADX'] >= min_adx) & (filtered_df['ADX'] <= max_adx)
        filtered_df['ADX_Filter_Pass'] = adx_valid & adx_in_range
        
        # 过滤结果
        passed_count = filtered_df['ADX_Filter_Pass'].sum()
        filtered_df = filtered_df[filtered_df['ADX_Filter_Pass']].copy()
        
        print(f"✅ ADX过滤完成: {len(self.original_df)} -> {len(filtered_df)} 条记录 "
              f"(通过率: {passed_count/len(self.original_df)*100:.1f}%)")
        
        return filtered_df
    
    def apply_confidence_filter_vectorized(self, min_confidence: float = 0.6, 
                                          max_confidence: float = 1.0) -> pd.DataFrame:
        """向量化置信度过滤"""
        print(f"🔍 应用置信度过滤 ({min_confidence:.3f} - {max_confidence:.3f})")
        
        # 向量化过滤条件
        confidence_mask = (
            (self.original_df['Confidence'] >= min_confidence) & 
            (self.original_df['Confidence'] <= max_confidence)
        )
        
        passed_count = confidence_mask.sum()
        filtered_df = self.original_df[confidence_mask].copy()
        
        print(f"✅ 置信度过滤完成: {len(self.original_df)} -> {len(filtered_df)} 条记录 "
              f"(通过率: {passed_count/len(self.original_df)*100:.1f}%)")
        
        return filtered_df
    
    def apply_open_loss_filter_optimized(self, max_open_losses: int = 2) -> pd.DataFrame:
        """优化版持仓浮亏过滤"""
        print(f"🔍 应用持仓浮亏过滤 (最大允许浮亏持仓数: {max_open_losses})")

        if self.price_data is None or self.price_data.empty:
            print("❌ 缺少价格数据，无法执行持仓浮亏过滤。跳过此过滤器。")
            return self.original_df.copy()

        # 准备价格查询
        close_prices = self.price_data['close'].sort_index()
        
        # 按时间排序
        trades_df = self.original_df.copy().sort_values('StartTimestamp').reset_index()
        
        accepted_indices = []
        open_trades = []
        
        # 批量处理，减少循环开销
        for i, trade in trades_df.iterrows():
            decision_time = trade['StartTimestamp']
            
            # 清理已平仓的交易
            open_trades = [t for t in open_trades if t['EndTimestamp'] > decision_time]
            
            # 获取当前价格
            current_price = close_prices.asof(decision_time)
            if pd.isna(current_price):
                continue
            
            # 计算浮亏数量
            open_loss_count = 0
            for open_trade in open_trades:
                entry_price = open_trade['StartPrice']
                if open_trade['Prediction'] == 1:  # 做多
                    if current_price < entry_price:
                        open_loss_count += 1
                else:  # 做空
                    if current_price > entry_price:
                        open_loss_count += 1
            
            # 决策
            if open_loss_count < max_open_losses:
                accepted_indices.append(trade['index'])
                open_trades.append(trade)
        
        if not accepted_indices:
            return pd.DataFrame(columns=self.original_df.columns)
            
        filtered_df = self.original_df.loc[accepted_indices].copy()
        
        original_len = len(self.original_df)
        passed_count = len(filtered_df)
        
        print(f"✅ 持仓浮亏过滤完成: {original_len} -> {passed_count} 条记录 "
              f"(通过率: {passed_count / original_len * 100:.1f}%)" if original_len > 0 else 
              "✅ 持仓浮亏过滤完成: 0 -> 0 条记录")
        
        return filtered_df
    
    def recalculate_capital_vectorized(self, filtered_df: pd.DataFrame, 
                                     initial_capital: float) -> pd.DataFrame:
        """向量化重新计算资金序列"""
        if filtered_df.empty:
            return filtered_df
        
        if len(filtered_df) == len(self.original_df):
            print("💰 使用原始资金序列...")
            if 'CapitalAfter_Filtered' not in filtered_df.columns:
                filtered_df['CapitalAfter_Filtered'] = filtered_df['CapitalAfter']
        else:
            print("💰 重新计算资金序列和收益指标...")
            
            # 向量化计算累积资金
            filtered_df = filtered_df.sort_values('StartTimestamp').copy()
            filtered_df['CapitalAfter_Filtered'] = initial_capital + filtered_df['ProfitLoss'].cumsum()
        
        # 向量化计算累积收益率
        filtered_df['CumulativeReturn'] = (
            (filtered_df['CapitalAfter_Filtered'] - initial_capital) / initial_capital * 100
        )
        
        return filtered_df
    
    def run_comprehensive_filter_optimized(self, config: Dict) -> Tuple[pd.DataFrame, Dict]:
        """运行优化版综合过滤策略"""
        print(f"\n🚀 开始综合过滤分析...")
        
        # 加载价格数据
        if 'coin' in config and 'interval' in config:
            self.load_price_volume_data(
                config['coin'], 
                config['interval'], 
                config.get('market', 'spot')
            )
        
        # 应用各种过滤器
        current_df = self.original_df.copy()
        
        # 1. 置信度过滤
        if config.get('confidence_filter', {}).get('enabled', False):
            conf_config = config['confidence_filter']
            current_df = self.apply_confidence_filter_vectorized(
                min_confidence=conf_config.get('min_confidence', 0.6),
                max_confidence=conf_config.get('max_confidence', 1.0)
            )
            self.original_df = current_df
        
        # 2. ADX过滤
        if config.get('adx_filter', {}).get('enabled', False):
            adx_config = config['adx_filter']
            current_df = self.apply_adx_filter_vectorized(
                adx_period=adx_config.get('adx_period', 14),
                min_adx=adx_config.get('min_adx', 25.0),
                max_adx=adx_config.get('max_adx', 70.0)
            )
            self.original_df = current_df
        
        # 3. 持仓浮亏过滤
        if config.get('consecutive_filter', {}).get('enabled', False):
            cons_config = config['consecutive_filter']
            current_df = self.apply_open_loss_filter_optimized(
                max_open_losses=cons_config.get('max_consecutive_losses', 3)
            )
            self.original_df = current_df
        
        # 重新计算资金和指标
        initial_capital = config.get('initial_capital', 1000)
        current_df = self.recalculate_capital_vectorized(current_df, initial_capital)
        
        # 计算性能指标
        filtered_metrics = self.calculate_performance_metrics(current_df, initial_capital)
        
        self.filtered_df = current_df
        return current_df, filtered_metrics
    
    def calculate_performance_metrics(self, filtered_df: pd.DataFrame, 
                                    initial_capital: float) -> Dict:
        """计算性能指标"""
        if filtered_df.empty:
            return {}
        
        capital_column = 'CapitalAfter_Filtered' if 'CapitalAfter_Filtered' in filtered_df.columns else 'CapitalAfter'
        
        final_capital = filtered_df[capital_column].iloc[-1]
        total_return = (final_capital - initial_capital) / initial_capital
        
        # 向量化计算胜负交易
        winning_mask = filtered_df['ProfitLoss'] > 0
        losing_mask = filtered_df['ProfitLoss'] < 0
        
        metrics = {
            'initial_capital': initial_capital,
            'final_capital': final_capital,
            'total_return': total_return,
            'total_return_pct': total_return * 100,
            'total_trades': len(filtered_df),
            'winning_trades': winning_mask.sum(),
            'losing_trades': losing_mask.sum(),
            'win_rate': winning_mask.mean() * 100,
        }
        
        # 向量化计算最大回撤
        capital_series = filtered_df[capital_column].values
        rolling_max = np.maximum.accumulate(capital_series)
        drawdown = (capital_series - rolling_max) / rolling_max
        metrics['max_drawdown_pct'] = np.min(drawdown) * 100
        
        # 计算盈亏比
        if winning_mask.any() and losing_mask.any():
            metrics['avg_win'] = filtered_df.loc[winning_mask, 'ProfitLoss'].mean()
            metrics['avg_loss'] = filtered_df.loc[losing_mask, 'ProfitLoss'].mean()
            metrics['profit_factor'] = abs(
                filtered_df.loc[winning_mask, 'ProfitLoss'].sum() / 
                filtered_df.loc[losing_mask, 'ProfitLoss'].sum()
            )
        else:
            metrics['avg_win'] = filtered_df.loc[winning_mask, 'ProfitLoss'].mean() if winning_mask.any() else 0
            metrics['avg_loss'] = filtered_df.loc[losing_mask, 'ProfitLoss'].mean() if losing_mask.any() else 0
            metrics['profit_factor'] = float('inf') if not losing_mask.any() else 0
        
        # 计算夏普比率
        if len(filtered_df) > 1:
            returns = filtered_df['ProfitLoss'] / initial_capital
            if returns.std() > 0:
                metrics['sharpe_ratio'] = returns.mean() / returns.std() * np.sqrt(len(returns))
            else:
                metrics['sharpe_ratio'] = 0
        else:
            metrics['sharpe_ratio'] = 0
        
        return metrics
    
    def print_comparison_report(self, original_metrics: Dict, filtered_metrics: Dict):
        """打印对比报告"""
        print("\n" + "="*60)
        print("📊 过滤前后对比报告")
        print("="*60)
        
        print(f"\n{'指标':<20} {'过滤前':<15} {'过滤后':<15} {'变化':<15}")
        print("-" * 65)
        
        # 交易数量
        orig_trades = original_metrics.get('total_trades', 0)
        filt_trades = filtered_metrics.get('total_trades', 0)
        change_trades = f"{filt_trades-orig_trades:+d}" if orig_trades > 0 else "N/A"
        print(f"{'交易数量':<20} {orig_trades:<15} {filt_trades:<15} {change_trades:<15}")
        
        # 总收益率
        orig_return = original_metrics.get('total_return_pct', 0)
        filt_return = filtered_metrics.get('total_return_pct', 0)
        change_return = f"{filt_return-orig_return:+.2f}%" if orig_return != 0 else "N/A"
        print(f"{'总收益率':<20} {orig_return:.2f}%{'':<8} {filt_return:.2f}%{'':<8} {change_return:<15}")
        
        # 胜率
        orig_winrate = original_metrics.get('win_rate', 0)
        filt_winrate = filtered_metrics.get('win_rate', 0)
        change_winrate = f"{filt_winrate-orig_winrate:+.2f}%" if orig_winrate != 0 else "N/A"
        print(f"{'胜率':<20} {orig_winrate:.2f}%{'':<8} {filt_winrate:.2f}%{'':<8} {change_winrate:<15}")
        
        # 最大回撤
        orig_dd = original_metrics.get('max_drawdown_pct', 0)
        filt_dd = filtered_metrics.get('max_drawdown_pct', 0)
        change_dd = f"{filt_dd-orig_dd:+.2f}%" if orig_dd != 0 else "N/A"
        print(f"{'最大回撤':<20} {orig_dd:.2f}%{'':<8} {filt_dd:.2f}%{'':<8} {change_dd:<15}")
        
        # 盈亏比
        orig_pf = original_metrics.get('profit_factor', 0)
        filt_pf = filtered_metrics.get('profit_factor', 0)
        orig_pf_str = f"{orig_pf:.2f}" if orig_pf != float('inf') else "∞"
        filt_pf_str = f"{filt_pf:.2f}" if filt_pf != float('inf') else "∞"
        print(f"{'盈亏比':<20} {orig_pf_str:<15} {filt_pf_str:<15} {'N/A':<15}")
        
        print("\n" + "="*60)


def main():
    """主函数 - 使用优化版过滤器"""
    parser = argparse.ArgumentParser(description="优化版回测结果过滤分析")
    parser.add_argument("--csv", default="backtest_money_log_quick.csv", help="回测结果CSV文件路径")
    parser.add_argument("--db", default="coin_data.db", help="SQLite数据库路径")
    parser.add_argument("--config", default="filter_config.json", help="过滤配置文件路径")
    parser.add_argument("--output", help="输出文件名前缀")
    
    # 快速过滤选项
    parser.add_argument("--min-confidence", type=float, help="最小置信度阈值")
    parser.add_argument("--min-adx", type=float, default=25.0, help="最小ADX值")
    parser.add_argument("--max-adx", type=float, default=70.0, help="最大ADX值")
    parser.add_argument("--adx-period", type=int, default=14, help="ADX计算周期")
    
    args = parser.parse_args()
    
    # 加载配置
    if os.path.exists(args.config):
        with open(args.config, 'r', encoding='utf-8') as f:
            config = json.load(f)
    else:
        config = {
            "coin": "ETH",
            "interval": "5m",
            "market": "spot",
            "initial_capital": 1000,
            "confidence_filter": {"enabled": False},
            "adx_filter": {"enabled": True, "adx_period": 14, "min_adx": 25.0, "max_adx": 70.0},
            "consecutive_filter": {"enabled": False}
        }
    
    # 命令行参数覆盖配置
    if args.min_confidence:
        config['confidence_filter']['min_confidence'] = args.min_confidence
        config['confidence_filter']['enabled'] = True
    
    if args.min_adx or args.max_adx or args.adx_period:
        if args.min_adx:
            config['adx_filter']['min_adx'] = args.min_adx
        if args.max_adx:
            config['adx_filter']['max_adx'] = args.max_adx
        if args.adx_period:
            config['adx_filter']['adx_period'] = args.adx_period
        config['adx_filter']['enabled'] = True
    
    # 创建优化版过滤器
    print("🚀 使用优化版过滤器...")
    filter_engine = OptimizedBacktestFilter(args.csv, args.db)
    
    # 计算原始指标
    original_df_with_capital = filter_engine.recalculate_capital_vectorized(
        filter_engine.original_df.copy(), config['initial_capital']
    )
    original_metrics = filter_engine.calculate_performance_metrics(
        original_df_with_capital, config['initial_capital']
    )
    
    # 运行优化版综合过滤
    import time
    start_time = time.time()
    
    filtered_df, filtered_metrics = filter_engine.run_comprehensive_filter_optimized(config)
    
    end_time = time.time()
    print(f"\n⚡ 过滤完成，耗时: {end_time - start_time:.2f} 秒")
    
    # 打印对比报告
    filter_engine.print_comparison_report(original_metrics, filtered_metrics)
    
    # 保存结果
    if args.output:
        output_prefix = args.output
    else:
        output_prefix = os.path.splitext(args.csv)[0] + "_optimized_filtered"
    
    filtered_csv = f"{output_prefix}.csv"
    filtered_df.to_csv(filtered_csv, index=False, float_format='%.4f')
    print(f"\n💾 过滤后结果已保存到: {filtered_csv}")
    
    print(f"\n✅ 优化版过滤分析完成！")


if __name__ == '__main__':
    main()