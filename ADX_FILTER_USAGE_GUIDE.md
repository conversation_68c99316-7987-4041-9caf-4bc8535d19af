# ADX过滤器使用指南

## 概述

ADX (Average Directional Index) 是一个衡量趋势强度的技术指标，由J<PERSON> <PERSON> Wilder开发。在交易策略中，ADX可以帮助我们识别市场是否处于趋势状态，从而过滤掉在震荡市场中的低质量信号。

## ADX指标说明

### 核心组件
- **+DI (Plus Directional Indicator)**: 上升方向指标
- **-DI (Minus Directional Indicator)**: 下降方向指标  
- **ADX**: 趋势强度指标，基于+DI和-DI计算得出

### ADX数值含义
- **ADX < 25**: 趋势较弱，市场可能处于震荡状态
- **25 ≤ ADX < 50**: 趋势适中，适合趋势跟踪策略
- **50 ≤ ADX < 70**: 趋势较强，趋势跟踪效果好
- **ADX > 70**: 趋势过强，可能面临反转风险

## 过滤器配置

### 配置文件示例 (filter_config.json)

```json
{
  "adx_filter": {
    "enabled": true,
    "adx_period": 14,
    "min_adx": 25.0,
    "max_adx": 70.0,
    "description": "ADX趋势强度过滤：min_adx以下认为趋势太弱，max_adx以上认为趋势过强可能反转"
  }
}
```

### 参数说明
- `enabled`: 是否启用ADX过滤
- `adx_period`: ADX计算周期，默认14
- `min_adx`: 最小ADX阈值，低于此值的信号将被过滤
- `max_adx`: 最大ADX阈值，高于此值的信号将被过滤

## 使用方法

### 1. 命令行使用

```bash
# 基本使用
python backtest_after_filter.py --csv backtest_results.csv --min-adx 25 --max-adx 70

# 指定ADX周期
python backtest_after_filter.py --csv backtest_results.csv --adx-period 21 --min-adx 30

# 与其他过滤器组合使用
python backtest_after_filter.py --csv backtest_results.csv \
    --min-confidence 0.65 \
    --min-adx 25 --max-adx 70 \
    --max-consecutive-losses 2
```

### 2. 配置文件使用

```bash
# 使用配置文件
python backtest_after_filter.py --csv backtest_results.csv --config filter_config.json
```

### 3. 程序化使用

```python
from backtest_after_filter import BacktestFilter

# 创建过滤器
filter_engine = BacktestFilter('backtest_results.csv', 'coin_data.db')

# 加载价格数据
filter_engine.load_price_volume_data('ETH', '5m', 'spot')

# 应用ADX过滤
filtered_df = filter_engine.apply_adx_filter(
    adx_period=14,
    min_adx=25.0,
    max_adx=70.0
)

# 查看过滤结果
print(f"原始信号数量: {len(filter_engine.original_df)}")
print(f"过滤后数量: {len(filtered_df)}")
```

## 过滤策略建议

### 1. 保守策略
```json
{
  "adx_period": 14,
  "min_adx": 30.0,
  "max_adx": 65.0
}
```
- 适用于风险厌恶型交易者
- 只在明确趋势中交易
- 过滤率较高，但信号质量更好

### 2. 平衡策略
```json
{
  "adx_period": 14,
  "min_adx": 25.0,
  "max_adx": 70.0
}
```
- 适用于大多数情况
- 平衡信号数量和质量
- 推荐的默认配置

### 3. 激进策略
```json
{
  "adx_period": 14,
  "min_adx": 20.0,
  "max_adx": 75.0
}
```
- 适用于高频交易
- 保留更多信号
- 需要配合其他过滤器使用

## 与其他过滤器的组合

### 推荐组合1: ADX + 置信度过滤
```json
{
  "confidence_filter": {
    "enabled": true,
    "min_confidence": 0.65
  },
  "adx_filter": {
    "enabled": true,
    "min_adx": 25.0,
    "max_adx": 70.0
  }
}
```

### 推荐组合2: ADX + 连续亏损控制
```json
{
  "adx_filter": {
    "enabled": true,
    "min_adx": 25.0,
    "max_adx": 70.0
  },
  "consecutive_filter": {
    "enabled": true,
    "max_consecutive_losses": 3
  }
}
```

## 性能优化建议

### 1. 周期选择
- **短周期 (7-10)**: 对趋势变化更敏感，适合短线交易
- **标准周期 (14)**: 平衡敏感性和稳定性，适合大多数情况
- **长周期 (21-28)**: 更稳定，适合中长线交易

### 2. 阈值调整
- **市场波动大**: 适当提高min_adx (30-35)
- **市场波动小**: 适当降低min_adx (20-25)
- **牛市**: 可以提高max_adx (75-80)
- **熊市**: 适当降低max_adx (60-65)

### 3. 不同时间框架的建议
- **1分钟**: min_adx=20, max_adx=75
- **5分钟**: min_adx=25, max_adx=70
- **15分钟**: min_adx=25, max_adx=70
- **1小时**: min_adx=30, max_adx=65

## 测试和验证

### 运行测试
```bash
python test_adx_filter.py
```

### 回测验证
```bash
# 对比ADX过滤前后的效果
python backtest_after_filter.py --csv your_backtest_results.csv --config filter_config.json
```

## 注意事项

1. **数据要求**: ADX计算需要完整的OHLC价格数据
2. **计算延迟**: ADX是滞后指标，需要足够的历史数据才能稳定
3. **市场适应性**: 不同市场和时间框架可能需要调整参数
4. **组合使用**: 建议与其他过滤器组合使用，避免单一指标的局限性

## 常见问题

### Q: ADX过滤器过滤掉了太多信号怎么办？
A: 可以适当降低min_adx阈值，或者调整max_adx上限。

### Q: 如何判断ADX参数是否合适？
A: 通过回测对比不同参数下的收益率、胜率、最大回撤等指标。

### Q: ADX过滤器适用于所有市场吗？
A: ADX在趋势明显的市场效果更好，在高频震荡市场可能需要调整参数。

### Q: 可以只使用ADX的方向信息吗？
A: 当前实现主要使用ADX强度，方向信息(+DI/-DI)已预留接口，可以根据需要启用。