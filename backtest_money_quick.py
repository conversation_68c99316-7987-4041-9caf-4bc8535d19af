# backtest_money_quick.py
# 使用已保存的模型，对SQLite中的历史数据进行带资金管理的真实环境模拟回测
# 支持 --start-time 和 --end-time 过滤数据
# (已根据用户建议优化止损逻辑)
# 新增 --quick 模式，预先计算所有特征以大幅加快回测速度

import pandas as pd
import numpy as np
import joblib
import json
import argparse
import os
import sqlite3
from datetime import datetime, timedelta
import pytz
from typing import Dict, List, Optional

# 引入 get_coin_history 中的表名生成工具
from get_coin_history import get_table_name

try:
    from model_utils_815 import calculate_features, get_coin_config, get_output_dir
    from analyze_money import analyze_and_plot_results
except ImportError:
    print("错误: 请确保 model_utils.py 和 analyze_money.py 文件存在。")
    # 提供备用函数以便脚本能独立运行
    def calculate_features(df, timeframe): print("正在计算特征..."); return df
    def get_coin_config(coin): print(f"获取 {coin} 配置..."); return {'model_basename': f"{coin}_model", 'api_symbol': coin.upper()}
    def get_output_dir(basename): os.makedirs('./output', exist_ok=True); return './output'
    def analyze_and_plot_results(df, basename): print(f"分析结果并绘图: {basename}")

# 北京时区
BEIJING_TZ = pytz.timezone('Asia/Shanghai')

def to_beijing_time(timestamp):
    if isinstance(timestamp, pd.Timestamp):
        if timestamp.tz is None:
            timestamp = timestamp.tz_localize('UTC')
        return timestamp.tz_convert(BEIJING_TZ)
    elif isinstance(timestamp, datetime):
        if timestamp.tzinfo is None:
            timestamp = pytz.UTC.localize(timestamp)
        return timestamp.astimezone(BEIJING_TZ)
    return timestamp

def format_beijing_time(timestamp):
    beijing_time = to_beijing_time(timestamp)
    return beijing_time.strftime('%Y-%m-%d %H:%M:%S UTC+8')

def parse_time_input(time_str):
    if not time_str:
        return None
    try:
        formats = ['%Y-%m-%d %H:%M:%S', '%Y-%m-%d %H:%M', '%Y-%m-%d', '%m-%d %H:%M', '%m-%d']
        dt = None
        for fmt in formats:
            try:
                dt = datetime.strptime(time_str, fmt)
                break
            except ValueError:
                continue
        if dt is None:
            raise ValueError(f"无法解析时间格式: {time_str}")
        if dt.year == 1900:
            dt = dt.replace(year=datetime.now().year)
        beijing_dt = BEIJING_TZ.localize(dt)
        utc_dt = beijing_dt.astimezone(pytz.UTC)
        return pd.Timestamp(utc_dt).tz_localize(None)
    except Exception as e:
        print(f"时间解析错误: {e}")
        return None

def load_chushou_config(chushou_file='chushou.json'):
    """加载出手时间配置"""
    if chushou_file is None or not os.path.exists(chushou_file):
        if chushou_file is not None:
            print(f"⚠️ 出手时间配置文件 '{chushou_file}' 不存在，将在所有时间开单")
        return None
    
    try:
        with open(chushou_file, 'r', encoding='utf-8') as f:
            chushou_config = json.load(f)
        
        # 转换为更易查询的格式
        time_filter = {}
        for day_config in chushou_config:
            day_name = day_config['StartDayName']
            hours = day_config['StartHour']
            time_filter[day_name] = set(hours)
        
        print(f"✅ 已加载出手时间配置，将仅在指定时间段开单")
        for day, hours in time_filter.items():
            print(f"  {day}: {sorted(list(hours))} 时")
        
        return time_filter
    except Exception as e:
        print(f"❌ 加载出手时间配置失败: {e}")
        return None

def is_good_time_to_trade(timestamp, time_filter):
    """检查当前时间是否适合开单"""
    if time_filter is None:
        return True
    
    beijing_time = to_beijing_time(timestamp)
    day_name = beijing_time.strftime('%A')
    hour = beijing_time.hour
    
    return day_name in time_filter and hour in time_filter[day_name]

def calculate_supertrend(df: pd.DataFrame, atr_period: int = 10, multiplier: float = 3.0) -> pd.DataFrame:
    """
    计算SuperTrend指标
    参数:
    - df: 包含 high, low, close 列的DataFrame
    - atr_period: ATR计算周期，默认10
    - multiplier: ATR倍数，默认3.0
    
    返回:
    - 包含 supertrend 和 trend 列的DataFrame
    """
    df = df.copy()
    
    # 计算True Range
    df['prev_close'] = df['close'].shift(1)
    df['tr1'] = df['high'] - df['low']
    df['tr2'] = abs(df['high'] - df['prev_close'])
    df['tr3'] = abs(df['low'] - df['prev_close'])
    df['tr'] = df[['tr1', 'tr2', 'tr3']].max(axis=1)
    
    # 计算ATR (使用SMA而不是EMA，与Pine脚本保持一致)
    df['atr'] = df['tr'].rolling(window=atr_period, min_periods=1).mean()
    
    # 计算HL2 (High + Low) / 2
    df['hl2'] = (df['high'] + df['low']) / 2
    
    # 计算基础上下轨
    df['basic_upper'] = df['hl2'] + (multiplier * df['atr'])
    df['basic_lower'] = df['hl2'] - (multiplier * df['atr'])
    
    # 初始化最终上下轨和趋势
    df['final_upper'] = df['basic_upper'].copy()
    df['final_lower'] = df['basic_lower'].copy()
    df['trend'] = 1  # 1表示上涨趋势，-1表示下跌趋势
    
    # 逐行计算SuperTrend（从第二行开始）
    for i in range(1, len(df)):
        # 计算最终上轨
        if (df.iloc[i]['basic_upper'] < df.iloc[i-1]['final_upper']) or (df.iloc[i-1]['close'] > df.iloc[i-1]['final_upper']):
            df.iloc[i, df.columns.get_loc('final_upper')] = df.iloc[i]['basic_upper']
        else:
            df.iloc[i, df.columns.get_loc('final_upper')] = df.iloc[i-1]['final_upper']
        
        # 计算最终下轨
        if (df.iloc[i]['basic_lower'] > df.iloc[i-1]['final_lower']) or (df.iloc[i-1]['close'] < df.iloc[i-1]['final_lower']):
            df.iloc[i, df.columns.get_loc('final_lower')] = df.iloc[i]['basic_lower']
        else:
            df.iloc[i, df.columns.get_loc('final_lower')] = df.iloc[i-1]['final_lower']
        
        # 确定趋势方向
        if (df.iloc[i-1]['trend'] == -1) and (df.iloc[i]['close'] > df.iloc[i]['final_lower']):
            df.iloc[i, df.columns.get_loc('trend')] = 1
        elif (df.iloc[i-1]['trend'] == 1) and (df.iloc[i]['close'] < df.iloc[i]['final_upper']):
            df.iloc[i, df.columns.get_loc('trend')] = -1
        else:
            df.iloc[i, df.columns.get_loc('trend')] = df.iloc[i-1]['trend']
    
    # 计算SuperTrend值
    df['supertrend'] = np.where(df['trend'] == 1, df['final_lower'], df['final_upper'])
    
    # 清理临时列
    columns_to_drop = ['prev_close', 'tr1', 'tr2', 'tr3', 'tr', 'hl2', 'basic_upper', 'basic_lower', 'final_upper', 'final_lower', 'atr']
    df = df.drop(columns=[col for col in columns_to_drop if col in df.columns], axis=1)
    
    return df[['supertrend', 'trend']]

def load_data_from_sqlite(db_path, coin, interval, market, price_multiplier=1.0, start_time=None, end_time=None):
    """从SQLite读取数据"""
    table_name = get_table_name(coin, interval, market)
    conn = sqlite3.connect(db_path)
    query = f"SELECT timestamp, open, high, low, close, volume FROM {table_name}"
    conditions = []
    params = []
    if start_time:
        conditions.append("timestamp >= ?")
        params.append(int(start_time.timestamp()))
    if end_time:
        conditions.append("timestamp <= ?")
        params.append(int(end_time.timestamp()))
    if conditions:
        query += " WHERE " + " AND ".join(conditions)
    query += " ORDER BY timestamp ASC"
    df = pd.read_sql_query(query, conn, params=params)
    conn.close()
    if df.empty:
        print("❌ 数据库中无符合条件的数据")
        return None
    df['timestamp'] = pd.to_datetime(df['timestamp'], unit='s', utc=True)
    df.set_index('timestamp', inplace=True)
    df = df.astype(float)
    if price_multiplier != 1.0:
        df[['open', 'high', 'low', 'close']] *= price_multiplier
    return df

def load_supertrend_data(db_path: str, coin: str, supertrend_interval: str, market: str, 
                        start_time: Optional[pd.Timestamp] = None, end_time: Optional[pd.Timestamp] = None,
                        atr_period: int = 10, multiplier: float = 3.0) -> Optional[pd.DataFrame]:
    """
    加载SuperTrend数据
    """
    print(f"📊 加载SuperTrend数据: {coin} {supertrend_interval} (ATR周期: {atr_period}, 倍数: {multiplier})")
    
    # 加载SuperTrend时间周期的数据
    supertrend_df = load_data_from_sqlite(db_path, coin, supertrend_interval, market, 1.0, start_time, end_time)
    if supertrend_df is None:
        print(f"❌ 无法加载SuperTrend数据: {coin} {supertrend_interval}")
        return None
    
    # 计算SuperTrend指标
    supertrend_indicators = calculate_supertrend(supertrend_df, atr_period, multiplier)
    
    # 合并到原数据
    supertrend_df = pd.concat([supertrend_df, supertrend_indicators], axis=1)
    
    print(f"✅ SuperTrend数据加载完成，共 {len(supertrend_df)} 条记录")
    return supertrend_df

def get_supertrend_signal(timestamp: pd.Timestamp, supertrend_df: pd.DataFrame) -> Optional[int]:
    """
    获取指定时间点的SuperTrend信号
    返回: 1表示看涨，-1表示看跌，None表示无数据
    """
    if supertrend_df is None or supertrend_df.empty:
        return None
    
    # 找到最接近但不超过当前时间的SuperTrend数据点
    valid_data = supertrend_df[supertrend_df.index <= timestamp]
    if valid_data.empty:
        return None
    
    latest_supertrend = valid_data.iloc[-2]
    return int(latest_supertrend['trend'])

def should_trade_with_supertrend(prediction: int, supertrend_signal: Optional[int], 
                               supertrend_filter: bool = False) -> bool:
    """
    根据SuperTrend信号决定是否应该交易
    
    参数:
    - prediction: 模型预测 (1=看涨, 0=看跌)
    - supertrend_signal: SuperTrend信号 (1=看涨, -1=看跌, None=无数据)
    - supertrend_filter: 是否启用SuperTrend过滤
    
    返回: True表示可以交易，False表示应该跳过
    """
    if not supertrend_filter or supertrend_signal is None:
        return True
    
    # SuperTrend看涨时只做多，看跌时只做空
    if prediction == 1 and supertrend_signal == 1:  # 模型预测涨 + SuperTrend看涨
        return True
    elif prediction == 0 and supertrend_signal == -1:  # 模型预测跌 + SuperTrend看跌
        return True
    else:
        return False

class HistoricalBacktester:
    def __init__(self, model_file: str, config_file: str, initial_capital: float, risk_per_trade_pct: float,
                 price_multiplier: float = 1.0, stop_loss_pct: float = None,
                 supertrend_df: Optional[pd.DataFrame] = None, supertrend_filter: bool = False,
                 enable_reverse_close: bool = False, reverse_close_min_positions: int = 1,
                 better_price_pct: float = 0.0):
        self.model_file = model_file
        self.config_file = config_file
        self.price_multiplier = price_multiplier
        self.stop_loss_pct = stop_loss_pct
        self.supertrend_df = supertrend_df
        self.supertrend_filter = supertrend_filter
        self.enable_reverse_close = enable_reverse_close
        self.reverse_close_min_positions = reverse_close_min_positions
        self.better_price_pct = better_price_pct / 100.0  # 转换为小数
        
        self.model = joblib.load(model_file)
        with open(config_file, 'r') as f:
            self.config = json.load(f)

        self.initial_capital = initial_capital
        self.current_capital = initial_capital
        self.risk_per_trade_pct = risk_per_trade_pct / 100.0

        self.active_predictions: Dict[str, dict] = {}
        self.completed_predictions: List[dict] = []
        self.prediction_counter = 0

        self.total_predictions = 0
        self.successful_predictions = 0
        self.failed_predictions = 0
        self.timeout_predictions = 0
        self.stop_loss_predictions = 0
        self.reverse_close_predictions = 0  # 新增：反向平仓的预测数
        self.supertrend_filtered_predictions = 0  # 新增：被SuperTrend过滤的预测数
        self.failed_order_predictions = 0  # 新增：挂单未成交的预测数

        print(f"回测器初始化完成")
        print(f"模型阈值: {self.config['best_threshold']:.3f}, 最大等待: {self.config['max_lookforward_minutes']}分钟")
        if stop_loss_pct is not None:
            print(f"止损触发条件: {stop_loss_pct:.1f}%")
        if supertrend_filter:
            print(f"🔍 SuperTrend过滤: 已启用 (看涨时只做多，看跌时只做空)")
        if enable_reverse_close:
            print(f"🔄 反向平仓: 已启用 (≥{reverse_close_min_positions}笔同向单时触发反向平仓)")
        if better_price_pct > 0:
            print(f"💰 挂单买入: 已启用 (期望更好价格: {better_price_pct*100:.2f}%)")
        print(f"初始资金: ${initial_capital:,.2f}")
        print(f"单次风险比例: {risk_per_trade_pct:.2f}%")

    def make_prediction_from_window(self, current_window_df: pd.DataFrame) -> tuple:
        """原始方法：从一个数据窗口计算特征并预测"""
        try:
            features_df = calculate_features(current_window_df, timeframe=self.config['timeframe_minutes'])
            features_df_clean = features_df.dropna()
            if features_df_clean.empty:
                return None, 0.0, 0.0
            feature_list = self.config['feature_list']
            latest_features_series = features_df_clean.iloc[-1]
            if any(f not in latest_features_series.index for f in feature_list):
                return None, 0.0, 0.0
            latest_features_df = latest_features_series[feature_list].to_frame().T
            current_price = latest_features_series['close']
            probability = self.model.predict_proba(latest_features_df)[0, 1]
            best_threshold = self.config['best_threshold']
            guess = None
            # if probability > best_threshold:
            #     guess = 1
            # elif probability < (1 - best_threshold):
            #     guess = 0
            if probability < (1 - best_threshold):
                guess = 0
            return guess, probability, current_price
        except Exception as e:
            print(f"预测时发生错误: {e}")
            return None, 0.0, 0.0

    def make_prediction_from_features(self, latest_features_series: pd.Series) -> tuple:
        """快速方法：直接使用预先计算好的特征行进行预测"""
        try:
            feature_list = self.config['feature_list']
            # 检查所需特征是否存在
            if any(f not in latest_features_series.index for f in feature_list) or latest_features_series[feature_list].isnull().any():
                return None, 0.0, 0.0
            
            latest_features_df = latest_features_series[feature_list].to_frame().T
            current_price = latest_features_series['close']
            probability = self.model.predict_proba(latest_features_df)[0, 1]
            best_threshold = self.config['best_threshold']
            
            guess = None
            # if probability > best_threshold:
            #     guess = 1
            # elif probability < (1 - best_threshold): # 假设预测0和1的阈值是对称的
            #     guess = 0
            if probability < (1 - best_threshold): # 假设预测0和1的阈值是对称的
                guess = 0
            return guess, probability, current_price
        except Exception as e:
            print(f"从特征预测时发生错误: {e}")
            return None, 0.0, 0.0

    def add_prediction(self, guess: int, probability: float, price: float, timestamp: pd.Timestamp, current_idx: int, supertrend_signal: Optional[int] = None):
        # 检查SuperTrend过滤
        if not should_trade_with_supertrend(guess, supertrend_signal, self.supertrend_filter):
            self.supertrend_filtered_predictions += 1
            return False  # 返回False表示预测被过滤

        self.prediction_counter += 1
        prediction_id = f"pred_{self.prediction_counter:06d}"
        trade_risk_capital = self.current_capital * self.risk_per_trade_pct
        max_wait_candles = self.config['max_lookforward_minutes'] // self.config['timeframe_minutes']

        # 计算挂单价格
        if self.better_price_pct > 0:
            # 做多时挂更低的价格，做空时挂更高的价格
            if guess == 1:  # 做多
                order_price = price * (1 - self.better_price_pct)
            else:  # 做空
                order_price = price * (1 + self.better_price_pct)
            status = 'pending'  # 挂单状态
            start_price = None  # 挂单状态下暂时没有成交价
        else:
            order_price = price
            status = 'active'  # 直接成交
            start_price = price  # 直接成交时设置起始价格

        prediction = {
            'id': prediction_id, 'guess': guess, 'probability': probability,
            'signal_price': price, 'order_price': order_price, 'start_price': start_price,
            'start_timestamp': timestamp, 'start_idx': current_idx, 'expire_idx': current_idx + max_wait_candles,
            'up_target': order_price * (1 + self.config['up_threshold']),
            'down_target': order_price * (1 - self.config['down_threshold']),
            'status': status, 'trade_risk_capital': trade_risk_capital,
            'max_loss_pct': 0.0, 'max_loss_price': order_price, 'max_loss_timestamp': timestamp,
            'max_profit_pct': 0.0, 'max_profit_price': order_price, 'max_profit_timestamp': timestamp,
            'supertrend_signal': supertrend_signal,  # 记录SuperTrend信号
            'filled_price': None, 'filled_timestamp': None, 'filled_idx': None  # 成交信息
        }
        self.active_predictions[prediction_id] = prediction
        self.total_predictions += 1
        direction_str = f"先涨{self.config['up_threshold']*100:.1f}%" if guess == 1 else f"先跌{self.config['down_threshold']*100:.1f}%"
        supertrend_str = f", ST:{supertrend_signal}" if supertrend_signal is not None else ""

        if status == 'pending':
            print(f"[{format_beijing_time(timestamp)}] 新挂单: {direction_str}, 信心: {probability:.3f}, 信号价: {price:.4f}, 挂单价: {order_price:.4f}, 风险暴露: ${trade_risk_capital:,.2f}{supertrend_str}")
        else:
            print(f"[{format_beijing_time(timestamp)}] 新预测: {direction_str}, 信心: {probability:.3f}, 价格: {price:.4f}, 风险暴露: ${trade_risk_capital:,.2f}{supertrend_str}")
        return True  # 返回True表示预测已添加

    def check_predictions(self, current_price: float, current_timestamp: pd.Timestamp, current_idx: int, current_signal: Optional[int] = None, current_high: float = None, current_low: float = None):
        completed_ids = []
        for pred_id, pred in self.active_predictions.items():
            # 检查挂单是否成交
            if pred['status'] == 'pending':
                filled = False
                if pred['guess'] == 1:  # 做多挂单，检查是否触及挂单价格
                    if current_low is not None and current_low <= pred['order_price']:
                        filled = True
                    elif current_low is None and current_price <= pred['order_price']:
                        filled = True
                else:  # 做空挂单，检查是否触及挂单价格
                    if current_high is not None and current_high >= pred['order_price']:
                        filled = True
                    elif current_high is None and current_price >= pred['order_price']:
                        filled = True

                if filled:
                    # 挂单成交，更新状态
                    pred['status'] = 'active'
                    pred['filled_price'] = pred['order_price']
                    pred['filled_timestamp'] = current_timestamp
                    pred['filled_idx'] = current_idx
                    pred['start_price'] = pred['order_price']  # 更新起始价格为成交价格
                    # 重新计算目标价格
                    pred['up_target'] = pred['order_price'] * (1 + self.config['up_threshold'])
                    pred['down_target'] = pred['order_price'] * (1 - self.config['down_threshold'])
                    # 重置最大盈亏记录
                    pred['max_loss_price'] = pred['order_price']
                    pred['max_profit_price'] = pred['order_price']
                    pred['max_loss_timestamp'] = current_timestamp
                    pred['max_profit_timestamp'] = current_timestamp

                    direction_str = f"做多" if pred['guess'] == 1 else f"做空"
                    print(f"[{format_beijing_time(current_timestamp)}] 挂单成交: {direction_str}, 成交价: {pred['order_price']:.4f}")
                else:
                    # 检查挂单是否超时
                    if current_idx >= pred['expire_idx']:
                        reason = f"挂单超时未成交(挂单价:{pred['order_price']:.4f})"
                        self.complete_prediction(pred_id, -4, current_price, current_timestamp, current_idx, reason)
                        completed_ids.append(pred_id)
                    continue  # 挂单未成交，跳过后续检查

            if pred['status'] != 'active':
                continue
            current_price_change_pct = (current_price - pred['start_price']) / pred['start_price'] * 100

            # 跟踪最大亏损
            if (pred['guess'] == 1 and current_price_change_pct < 0) or \
               (pred['guess'] == 0 and current_price_change_pct > 0):
                loss_pct = abs(current_price_change_pct)
                if loss_pct > pred['max_loss_pct']:
                    pred.update({'max_loss_pct': loss_pct, 'max_loss_price': current_price, 'max_loss_timestamp': current_timestamp})

            # 跟踪最大盈利
            if (pred['guess'] == 1 and current_price_change_pct > 0) or \
               (pred['guess'] == 0 and current_price_change_pct < 0):
                profit_pct = abs(current_price_change_pct)
                if profit_pct > pred['max_profit_pct']:
                    pred.update({'max_profit_pct': profit_pct, 'max_profit_price': current_price, 'max_profit_timestamp': current_timestamp})

            # 1. 反向平仓检查 - 优先级最高
            if self.enable_reverse_close and current_signal is not None:
                # 统计当前同向活跃预测数量
                same_direction_count = sum(1 for p in self.active_predictions.values()
                                         if p['status'] == 'active' and p['guess'] == pred['guess'])

                # 检查是否应该反向平仓
                should_reverse_close = (pred['guess'] == 1 and current_signal == 0) or \
                                     (pred['guess'] == 0 and current_signal == 1)

                if should_reverse_close and same_direction_count >= self.reverse_close_min_positions:
                    reason = f"反向平仓(原:{['看跌','看涨'][pred['guess']]},新:{['看跌','看涨'][current_signal]})"
                    self.complete_prediction(pred_id, -3, current_price, current_timestamp, current_idx, reason)
                    completed_ids.append(pred_id)
                    continue

            # 2. 止损检查
            if self.stop_loss_pct is not None:
                should_stop_loss = (pred['guess'] == 1 and current_price_change_pct < -self.stop_loss_pct) or \
                                   (pred['guess'] == 0 and current_price_change_pct > self.stop_loss_pct)
                if should_stop_loss:
                    reason = f"止损(触发点:{-self.stop_loss_pct:.1f}%)"
                    self.complete_prediction(pred_id, -2, current_price, current_timestamp, current_idx, reason)
                    completed_ids.append(pred_id)
                    continue

            if current_price >= pred['up_target']:
                result, reason = (1, "达到上涨目标") if pred['guess'] == 1 else (0, "达到上涨目标(预测错误)")
                self.complete_prediction(pred_id, result, current_price, current_timestamp, current_idx, reason)
                completed_ids.append(pred_id)
            elif current_price <= pred['down_target']:
                result, reason = (1, "达到下跌目标") if pred['guess'] == 0 else (0, "达到下跌目标(预测错误)")
                self.complete_prediction(pred_id, result, current_price, current_timestamp, current_idx, reason)
                completed_ids.append(pred_id)
            elif current_idx >= pred['expire_idx']:
                self.complete_prediction(pred_id, -1, current_price, current_timestamp, current_idx, "超时")
                completed_ids.append(pred_id)
        
        for pred_id in completed_ids:
            if pred_id in self.active_predictions:
                del self.active_predictions[pred_id]
    # 可能会超过1,-1,因为是动态止盈止损
    # def calculate_score(self, prediction: int, start_price: float, end_price: float, result: int):
    #     price_change_pct = (end_price - start_price) / start_price
    #     threshold = self.config.get('up_threshold', 0.01) if prediction == 1 else self.config.get('down_threshold', 0.01)
        
    #     if result == 1: return 1.0
    #     if result == 0: return -1.0
        
    #     # 对于超时或止损，根据最终价格变化方向计算得分
    #     score = price_change_pct / threshold if prediction == 1 else -price_change_pct / threshold
    #     return max(-1.0, min(1.0, score))
    def calculate_score(self, prediction: int, start_price: float, end_price: float, result: int):
        price_change_pct = (end_price - start_price) / start_price
        threshold = self.config.get('up_threshold', 0.01) if prediction == 1 else self.config.get('down_threshold', 0.01)
        
        # 对于超时或止损，根据最终价格变化方向计算得分,
        # 考虑了0.001的手续费，实际情况内部换仓会更小。
        score = (price_change_pct-0.001) / threshold if prediction == 1 else (-price_change_pct+0.001) / threshold
        return score
    def complete_prediction(self, pred_id: str, result: int, final_price: float, end_timestamp: pd.Timestamp, end_idx: int, reason: str, custom_score: float = None):
        if pred_id not in self.active_predictions: return
        pred = self.active_predictions[pred_id]
        pred['status'] = 'completed'

        # 获取起始价格：如果有start_price用start_price，否则用order_price
        start_price = pred.get('start_price') or pred.get('order_price', 0)

        # 挂单失败时不计算盈亏，因为没有实际交易发生
        if result == -4:  # 挂单失败
            score = 0.0
            profit_loss = 0.0
        else:
            score = custom_score if custom_score is not None else self.calculate_score(pred['guess'], start_price, final_price, result)
            # 计算真实分数和盈亏，之前是按5%来的。
            # threshold = self.config.get('up_threshold', 0.01)
            # percent = threshold/0.05
            # 还是选择调整杠杆算了，这样仓位好计算百分比一点。
            profit_loss = pred['trade_risk_capital'] * score

        self.current_capital += profit_loss

        if result == 1: self.successful_predictions += 1; status_str = "成功✅"
        elif result == 0: self.failed_predictions += 1; status_str = "失败❌"
        elif result == -2: self.stop_loss_predictions += 1; status_str = "止损🛑"
        elif result == -3: self.reverse_close_predictions += 1; status_str = "反向平仓🔄"
        elif result == -4: self.failed_order_predictions += 1; status_str = "挂单失败📋"
        else: self.timeout_predictions += 1; status_str = "超时⏰"

        # 添加时间统计信息
        start_beijing = to_beijing_time(pred['start_timestamp'])
        end_beijing = to_beijing_time(end_timestamp)
        
        # 获取起始价格：如果有start_price用start_price，否则用order_price
        start_price_for_calc = pred.get('start_price') or pred.get('order_price', 0)
        completed_pred = {
            'PredictionID': pred['id'], 'StartTimestamp': format_beijing_time(pred['start_timestamp']),
            'EndTimestamp': format_beijing_time(end_timestamp), 'StartPrice': start_price_for_calc,
            'EndPrice': final_price, 'PriceChangePct': ((final_price - start_price_for_calc) / max(start_price_for_calc, 0.0001)) * 100,
            'MaxLossPct': pred['max_loss_pct'], 'MaxLossPrice': pred['max_loss_price'],
            'MaxLossTimestamp': format_beijing_time(pred['max_loss_timestamp']),
            'MaxProfitPct': pred['max_profit_pct'], 'MaxProfitPrice': pred['max_profit_price'],
            'MaxProfitTimestamp': format_beijing_time(pred['max_profit_timestamp']), 'Confidence': pred['probability'],
            'Prediction': pred['guess'], 'Result': result, 'Score': score,
            'ProfitLoss': profit_loss, 'CapitalAfter': self.current_capital,
            'Status': status_str, 'Reason': reason,
            'DurationMinutes': (end_idx - pred['start_idx']) * self.config['timeframe_minutes'],
            'UpTarget': pred['up_target'], 'DownTarget': pred['down_target'],
            'SuperTrendSignal': pred.get('supertrend_signal', None),  # 新增SuperTrend信号记录
            # 新增挂单相关字段
            'SignalPrice': pred.get('signal_price', start_price_for_calc),
            'OrderPrice': pred.get('order_price', start_price_for_calc),
            'FilledPrice': pred.get('filled_price', None),
            'FilledTimestamp': format_beijing_time(pred['filled_timestamp']) if pred.get('filled_timestamp') else None,
            # 新增时间统计字段
            'StartHour': start_beijing.hour,
            'StartDayOfWeek': start_beijing.weekday(),  # 0=Monday, 6=Sunday
            'StartDayName': start_beijing.strftime('%A'),  # Monday, Tuesday, etc.
            'EndHour': end_beijing.hour,
            'EndDayOfWeek': end_beijing.weekday(),
            'EndDayName': end_beijing.strftime('%A')
        }
        self.completed_predictions.append(completed_pred)
        direction_str = f"先涨..." if pred['guess'] == 1 else f"先跌..."
        max_loss_info = f", 最大亏损: {pred['max_loss_pct']:.2f}%" if pred['max_loss_pct'] > 0 else ""
        max_profit_info = f", 最大盈利: {pred['max_profit_pct']:.2f}%" if pred['max_profit_pct'] > 0 else ""
        print(f"[{format_beijing_time(end_timestamp)}] 预测完成: {direction_str} -> {status_str}, "
              f"得分: {score:+.2f}, 盈亏: ${profit_loss:+.2f}, "
              f"当前资金: ${self.current_capital:,.2f}{max_loss_info}{max_profit_info}")

def calculate_performance_metrics(backtester: HistoricalBacktester, initial_capital: float) -> dict:
    """计算详细的性能指标"""
    metrics = {}
    
    final_capital = backtester.current_capital
    total_return = (final_capital - initial_capital) / initial_capital
    
    # 基础指标
    metrics['initial_capital'] = initial_capital
    metrics['final_capital'] = final_capital
    metrics['total_return'] = total_return
    metrics['total_return_pct'] = total_return * 100
    
    if not backtester.completed_predictions:
        return metrics
    
    # 构建资金序列
    capital_series = [initial_capital]
    returns_series = []
    
    for pred in backtester.completed_predictions:
        capital_series.append(pred['CapitalAfter'])
        # 计算单次交易收益率
        trade_return = pred['ProfitLoss'] / capital_series[-2]  # 基于交易前资金计算收益率
        returns_series.append(trade_return)
    
    if len(returns_series) == 0:
        return metrics
    
    returns_array = np.array(returns_series)
    capital_array = np.array(capital_series)
    
    # 计算最大回撤
    rolling_max = np.maximum.accumulate(capital_array)
    drawdown = (capital_array - rolling_max) / rolling_max
    max_drawdown = np.min(drawdown)
    
    metrics['max_drawdown'] = max_drawdown
    metrics['max_drawdown_pct'] = max_drawdown * 100
    
    # 计算夏普比率 (简化版，假设无风险利率为0)
    if len(returns_array) > 1 and np.std(returns_array) > 0:
        sharpe_ratio = np.mean(returns_array) / np.std(returns_array) * np.sqrt(len(returns_array))
    else:
        sharpe_ratio = 0.0
    
    metrics['sharpe_ratio'] = sharpe_ratio
    
    # 计算 Sortino Ratio
    # Sortino Ratio = (平均收益 - 目标收益) / 下行标准差
    target_return = 0.0  # 目标收益率设为0
    downside_returns = returns_array[returns_array < target_return]
    
    if len(downside_returns) > 0:
        downside_std = np.std(downside_returns)
        if downside_std > 0:
            sortino_ratio = (np.mean(returns_array) - target_return) / downside_std * np.sqrt(len(returns_array))
        else:
            sortino_ratio = float('inf') if np.mean(returns_array) > target_return else 0.0
    else:
        # 没有负收益，Sortino比率为无穷大或基于总体标准差
        sortino_ratio = float('inf') if np.mean(returns_array) > target_return else 0.0
    
    metrics['sortino_ratio'] = sortino_ratio
    
    # 计算 Calmar Ratio
    # Calmar Ratio = 年化收益率 / 最大回撤的绝对值
    if abs(max_drawdown) > 0:
        # 假设交易周期，计算年化收益率
        # 这里简化处理，假设每个交易代表一定的时间周期
        if len(backtester.completed_predictions) > 0:
            # 计算平均交易持续时间（分钟）
            avg_duration_minutes = np.mean([pred['DurationMinutes'] for pred in backtester.completed_predictions])
            # 估算年化因子（一年有525600分钟）
            trades_per_year = 525600 / avg_duration_minutes if avg_duration_minutes > 0 else 1
            annualized_return = total_return * (trades_per_year / len(backtester.completed_predictions))
            calmar_ratio = annualized_return / abs(max_drawdown)
        else:
            calmar_ratio = 0.0
    else:
        calmar_ratio = float('inf') if total_return > 0 else 0.0
    
    metrics['calmar_ratio'] = calmar_ratio
    
    # 胜率和其他统计
    metrics['win_rate'] = backtester.successful_predictions / max(backtester.total_predictions, 1)
    metrics['win_rate_pct'] = metrics['win_rate'] * 100
    
    # 平均盈利和亏损
    winning_trades = [pred['ProfitLoss'] for pred in backtester.completed_predictions if pred['ProfitLoss'] > 0]
    losing_trades = [pred['ProfitLoss'] for pred in backtester.completed_predictions if pred['ProfitLoss'] < 0]
    
    metrics['avg_win'] = np.mean(winning_trades) if winning_trades else 0.0
    metrics['avg_loss'] = np.mean(losing_trades) if losing_trades else 0.0
    metrics['profit_factor'] = abs(sum(winning_trades) / sum(losing_trades)) if losing_trades and sum(losing_trades) != 0 else float('inf')
    
    return metrics

def finalize_and_report(backtester: HistoricalBacktester, initial_capital: float, stop_loss_pct: float | None):
    """通用报告函数"""
    print("\n=== 回测结果摘要 ===")
    print(f"总预测数: {backtester.total_predictions}, 成功: {backtester.successful_predictions}, "
          f"失败: {backtester.failed_predictions}, 超时: {backtester.timeout_predictions}", end="")

    # 添加止损、反向平仓和挂单失败统计
    additional_stats = []
    if stop_loss_pct is not None:
        additional_stats.append(f"止损: {backtester.stop_loss_predictions}")
    if backtester.enable_reverse_close:
        additional_stats.append(f"反向平仓: {backtester.reverse_close_predictions}")
    if backtester.better_price_pct > 0:
        additional_stats.append(f"挂单失败: {backtester.failed_order_predictions}")

    if additional_stats:
        print(f", {', '.join(additional_stats)}")
    else:
        print()
    
    # 显示SuperTrend过滤统计
    if backtester.supertrend_filter and backtester.supertrend_filtered_predictions > 0:
        print(f"🔍 SuperTrend过滤: {backtester.supertrend_filtered_predictions} 个预测被过滤")
    
    # 计算详细性能指标
    metrics = calculate_performance_metrics(backtester, initial_capital)
    
    print(f"\n=== 基础指标 ===")
    print(f"初始资金: ${metrics['initial_capital']:,.2f}")
    print(f"最终资金: ${metrics['final_capital']:,.2f}")
    print(f"总收益率: {metrics['total_return_pct']:+.2f}%")
    print(f"胜率: {metrics['win_rate_pct']:.2f}%")
    
    if backtester.completed_predictions:
        print(f"\n=== 风险指标 ===")
        print(f"最大回撤: {metrics['max_drawdown_pct']:.2f}%")
        print(f"夏普比率: {metrics['sharpe_ratio']:.3f}")
        
        # Sortino Ratio 显示
        if metrics['sortino_ratio'] == float('inf'):
            print(f"Sortino比率: ∞ (无负收益)")
        else:
            print(f"Sortino比率: {metrics['sortino_ratio']:.3f}")
        
        # Calmar Ratio 显示
        if metrics['calmar_ratio'] == float('inf'):
            print(f"Calmar比率: ∞ (无回撤)")
        else:
            print(f"Calmar比率: {metrics['calmar_ratio']:.3f}")
        
        print(f"\n=== 交易统计 ===")
        print(f"平均盈利: ${metrics['avg_win']:+.2f}")
        print(f"平均亏损: ${metrics['avg_loss']:+.2f}")
        if metrics['profit_factor'] == float('inf'):
            print(f"盈亏比: ∞ (无亏损交易)")
        else:
            print(f"盈亏比: {metrics['profit_factor']:.2f}")

    if backtester.completed_predictions:
        results_df = pd.DataFrame(backtester.completed_predictions)
        # 确保时间戳列是datetime类型以便正确排序
        results_df['_sort_time'] = pd.to_datetime(results_df['StartTimestamp'].str.replace(' UTC\\+8', '', regex=True))
        results_df = results_df.sort_values('_sort_time').drop('_sort_time', axis=1)
        
        # 添加性能指标到CSV（作为元数据）
        metrics_df = pd.DataFrame([metrics])
        
        output_filename = "backtest_money_log_quick.csv"
        results_df.to_csv(output_filename, index=False, float_format='%.4f')
        
        # 保存性能指标到单独文件
        metrics_filename = "backtest_performance_metrics.csv"
        metrics_df.to_csv(metrics_filename, index=False, float_format='%.6f')
        
        print(f"\n📊 详细回测日志已保存到: {output_filename}")
        print(f"📈 性能指标已保存到: {metrics_filename}")
        
        analyze_and_plot_results(results_df, "backtest_money_quick")
    else:
        print("\n没有生成任何预测结果。")

def run_backtest_original(model_file, config_file, df, **kwargs):
    """原始的逐条回测方法"""
    print("=== 开始精确回测(逐条计算特征) ===")
    backtester = HistoricalBacktester(
        model_file, config_file, kwargs['initial_capital'], kwargs['risk_per_trade_pct'],
        kwargs['price_multiplier'], kwargs['stop_loss_pct'],
        kwargs.get('supertrend_df'), kwargs.get('supertrend_filter', False),
        kwargs.get('enable_reverse_close', False), kwargs.get('reverse_close_min_positions', 1),
        kwargs.get('better_price_pct', 0.0)
    )
    
    # 加载出手时间配置
    time_filter = load_chushou_config(kwargs.get('chushou_file', 'chushou.json'))
    
    # 需要足够历史数据来计算初始特征
    min_history = 720 // backtester.config.get('timeframe_minutes', 5) + 50 
    prediction_window_size = 1000 
    if prediction_window_size < min_history:
        prediction_window_size = min_history
        
    actual_start_pos = prediction_window_size
    if actual_start_pos >= len(df):
        print(f"❌ 数据不足以进行回测。需要至少 {actual_start_pos} 条数据，但只有 {len(df)} 条。")
        return

    print(f"\n从索引 {actual_start_pos} 开始预测 (时间: {format_beijing_time(df.index[actual_start_pos])})")
    print(f"将同时保持最多 {kwargs['max_active_predictions']} 笔活跃的投资。")

    skipped_predictions = 0
    for i in range(actual_start_pos, len(df)):
        current_timestamp, current_price = df.index[i], df.iloc[i]['close']
        current_high, current_low = df.iloc[i]['high'], df.iloc[i]['low']
        backtester.check_predictions(current_price, current_timestamp, i, None, current_high, current_low)
        
        active_count = len([p for p in backtester.active_predictions.values() if p['status'] == 'active'])
        if active_count < kwargs['max_active_predictions']:
            # 检查是否在允许的交易时间内
            if is_good_time_to_trade(current_timestamp, time_filter):
                start_slice_index = i - prediction_window_size + 1
                current_window_df = df.iloc[start_slice_index : i+1].copy()
                guess, probability, pred_price = backtester.make_prediction_from_window(current_window_df)
                if guess is not None:
                    # 获取SuperTrend信号
                    supertrend_signal = get_supertrend_signal(current_timestamp, backtester.supertrend_df)
                    backtester.add_prediction(guess, probability, pred_price, current_timestamp, i, supertrend_signal)
            else:
                # 统计跳过的预测数量（仅在有预测信号时计数）
                start_slice_index = i - prediction_window_size + 1
                current_window_df = df.iloc[start_slice_index : i+1].copy()
                guess, _, _ = backtester.make_prediction_from_window(current_window_df)
                if guess is not None:
                    skipped_predictions += 1

    # 结束所有仍在活跃的预测
    final_timestamp, final_price, final_idx = df.index[-1], df.iloc[-1]['close'], len(df) - 1
    for pred_id in list(backtester.active_predictions.keys()):
        backtester.complete_prediction(pred_id, -1, final_price, final_timestamp, final_idx, "数据结束-超时")

    if time_filter and skipped_predictions > 0:
        print(f"\n⏰ 因时间过滤跳过了 {skipped_predictions} 个潜在预测信号")
    
    finalize_and_report(backtester, kwargs['initial_capital'], kwargs['stop_loss_pct'])


def run_quick_backtest(model_file, config_file, df, **kwargs):
    """新增的快速回测方法"""
    print("=== 开始快速回测(预计算特征) ===")
    backtester = HistoricalBacktester(
        model_file, config_file, kwargs['initial_capital'], kwargs['risk_per_trade_pct'],
        kwargs['price_multiplier'], kwargs['stop_loss_pct'],
        kwargs.get('supertrend_df'), kwargs.get('supertrend_filter', False),
        kwargs.get('enable_reverse_close', False), kwargs.get('reverse_close_min_positions', 1),
        kwargs.get('better_price_pct', 0.0)
    )

    # 加载出手时间配置
    time_filter = load_chushou_config(kwargs.get('chushou_file', 'chushou.json'))

    # 1. 一次性计算所有特征
    print("正在预先计算所有时间序列的特征，请稍候...")
    features_df = calculate_features(df, timeframe=backtester.config['timeframe_minutes'])
    print("特征计算完成。")
    
    # 找到第一个可以开始预测的有效点
    # dropna()会移除任何包含NaN的行，这通常是数据序列的开头部分
    valid_features_df = features_df.dropna(subset=backtester.config['feature_list'])
    if valid_features_df.empty:
        print("❌ 计算特征后没有剩下任何有效数据行，无法进行回测。")
        return
    
    first_valid_index_pos = df.index.get_loc(valid_features_df.index[0])
    print(f"\n从索引 {first_valid_index_pos} 开始预测 (时间: {format_beijing_time(valid_features_df.index[0])})")
    print(f"将同时保持最多 {kwargs['max_active_predictions']} 笔活跃的投资。")

    # 2. 遍历数据进行回测
    skipped_predictions = 0
    for i in range(first_valid_index_pos, len(df)):
        current_timestamp = df.index[i]
        current_price = df.iloc[i]['close']
        current_high, current_low = df.iloc[i]['high'], df.iloc[i]['low']

        # 获取当前信号（如果启用反向平仓模式）
        current_signal = None
        if backtester.enable_reverse_close and current_timestamp in features_df.index:
            latest_features_series = features_df.loc[current_timestamp]
            current_signal, _, _ = backtester.make_prediction_from_features(latest_features_series)

        # 检查现有预测的状态
        backtester.check_predictions(current_price, current_timestamp, i, current_signal, current_high, current_low)

        # 检查是否可以进行新预测
        active_count = len([p for p in backtester.active_predictions.values() if p['status'] == 'active'])
        if active_count < kwargs['max_active_predictions']:
            # 直接从预计算的特征DataFrame中获取当前行的特征
            if current_timestamp in features_df.index:
                latest_features_series = features_df.loc[current_timestamp]
                guess, probability, pred_price = backtester.make_prediction_from_features(latest_features_series)
                
                if guess is not None:
                    # 检查是否在允许的交易时间内
                    if is_good_time_to_trade(current_timestamp, time_filter):
                        # 获取SuperTrend信号
                        supertrend_signal = get_supertrend_signal(current_timestamp, backtester.supertrend_df)
                        backtester.add_prediction(guess, probability, pred_price, current_timestamp, i, supertrend_signal)
                    else:
                        skipped_predictions += 1
    
    # 3. 结束所有仍在活跃的预测
    final_timestamp, final_price, final_idx = df.index[-1], df.iloc[-1]['close'], len(df) - 1
    for pred_id in list(backtester.active_predictions.keys()):
        backtester.complete_prediction(pred_id, -1, final_price, final_timestamp, final_idx, "数据结束-超时")
    
    if time_filter and skipped_predictions > 0:
        print(f"\n⏰ 因时间过滤跳过了 {skipped_predictions} 个潜在预测信号")
    
    # 4. 报告结果
    finalize_and_report(backtester, kwargs['initial_capital'], kwargs['stop_loss_pct'])


if __name__ == '__main__':
    parser = argparse.ArgumentParser(description="使用资金管理模型对SQLite历史数据进行回测。")
    parser.add_argument("--coin", default="ETH", help="交易对，例如 BTC")
    parser.add_argument("--interval", default="15m", help="K线间隔，例如 15m")
    parser.add_argument("--market", default="spot", choices=["spot", "futures"], help="市场类型")
    parser.add_argument("--db", default="coin_data.db", help="SQLite数据库路径")
    parser.add_argument("--start-time", help="回测开始时间(北京时间, YYYY-MM-DD HH:MM)")
    parser.add_argument("--end-time", help="回测结束时间(北京时间, YYYY-MM-DD HH:MM)")
    parser.add_argument("--stop-loss", type=float, help="止损百分比 (例如, 输入 2.5 表示 2.5%%)")
    parser.add_argument("--max-active-predictions", type=int, default=1000, help="最大同时活跃预测数")
    parser.add_argument("--initial-capital", type=float, default=1000, help="初始资金")
    parser.add_argument("--risk-per-trade", type=float, default=1.0, help="单次交易风险比例(%%)")
    parser.add_argument("--model-file", help="模型文件路径 (.joblib)")
    parser.add_argument("--config-file", help="配置文件路径 (.json)")
    parser.add_argument("--quick", action='store_true', help="启用快速回测模式 (预计算所有特征)")
    parser.add_argument("--chushou-file", default="chushou.json", help="出手时间配置文件路径 (默认: chushou.json)")
    parser.add_argument("--use-chushou", action='store_true', help="启用出手时间过滤，仅在高胜率时间段开单")
    
    # SuperTrend 相关参数
    parser.add_argument("--use-supertrend", action='store_true', help="启用SuperTrend过滤 (看涨时只做多，看跌时只做空)")
    parser.add_argument("--supertrend-interval", default="30m", help="SuperTrend计算使用的K线间隔 (默认: 15m)")
    parser.add_argument("--supertrend-atr-period", type=int, default=13, help="SuperTrend ATR计算周期 (默认: 10)")
    parser.add_argument("--supertrend-multiplier", type=float, default=3.8, help="SuperTrend ATR倍数 (默认: 3.0)")

    # 反向平仓相关参数
    parser.add_argument("--enable-reverse-close", action='store_true', help="启用反向平仓 (当触发反向仓位时自动平仓)")
    parser.add_argument("--reverse-close-min-positions", type=int, default=1, help="触发反向平仓的最小同向仓位数 (默认: 1)")

    # 挂单买入相关参数
    parser.add_argument("--better-price-pct", type=float, default=0.0, help="期望更好价格的百分比 (例如: 0.1 表示期望0.1%%更好的价格)")

    args = parser.parse_args()

    start_time = parse_time_input(args.start_time) if args.start_time else None
    end_time = parse_time_input(args.end_time) if args.end_time else None

    # 获取币种配置
    coin_config = get_coin_config(args.coin)
    if coin_config is None:
        exit(1)

    model_file = args.model_file if args.model_file else f"models/{coin_config['model_basename']}_model.joblib"
    config_file = args.config_file if args.config_file else f"models/{coin_config['model_basename']}_config.json"
    
    if not os.path.exists(model_file) or not os.path.exists(config_file):
        print(f"❌ 错误: 模型文件 '{model_file}' 或配置文件 '{config_file}' 不存在。")
        exit(1)

    # 加载主要回测数据
    df = load_data_from_sqlite(args.db, coin_config['api_symbol'], args.interval, args.market, 
                              price_multiplier=1.0, start_time=start_time, end_time=end_time)
    
    if df is None or df.empty:
        print("❌ 未加载到任何数据，程序退出。")
        exit(1)

    # 加载SuperTrend数据（如果启用）
    supertrend_df = None
    if args.use_supertrend:
        supertrend_df = load_supertrend_data(
            args.db, coin_config['api_symbol'], args.supertrend_interval, args.market,
            start_time, end_time, args.supertrend_atr_period, args.supertrend_multiplier
        )
        if supertrend_df is None:
            print("❌ 无法加载SuperTrend数据，程序退出。")
            exit(1)

    backtest_params = {
        'initial_capital': args.initial_capital,
        'risk_per_trade_pct': args.risk_per_trade,
        'price_multiplier': 1.0,
        'stop_loss_pct': args.stop_loss,
        'max_active_predictions': args.max_active_predictions,
        'chushou_file': args.chushou_file if args.use_chushou else None,
        'supertrend_df': supertrend_df,
        'supertrend_filter': args.use_supertrend,
        'enable_reverse_close': args.enable_reverse_close,
        'reverse_close_min_positions': args.reverse_close_min_positions,
        'better_price_pct': args.better_price_pct
    }

    if args.quick:
        run_quick_backtest(model_file, config_file, df, **backtest_params)
    else:
        run_backtest_original(model_file, config_file, df, **backtest_params)