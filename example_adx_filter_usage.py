#!/usr/bin/env python3
"""
ADX过滤器实际使用示例
演示如何在真实回测结果上应用ADX过滤
"""

import pandas as pd
import numpy as np
from backtest_after_filter import BacktestFilter
import json
import os

def example_basic_adx_filter():
    """基础ADX过滤示例"""
    print("🔍 基础ADX过滤示例")
    print("="*50)
    
    # 检查是否有真实的回测结果文件
    csv_file = "backtest_money_log_quick.csv"
    if not os.path.exists(csv_file):
        print(f"❌ 找不到回测结果文件: {csv_file}")
        print("请先运行回测生成结果文件")
        return
    
    # 创建过滤器
    filter_engine = BacktestFilter(csv_file, "coin_data.db")
    
    # 加载价格数据
    filter_engine.load_price_volume_data('ETH', '5m', 'spot')
    
    # 应用ADX过滤
    filtered_df = filter_engine.apply_adx_filter(
        adx_period=14,      # ADX计算周期
        min_adx=25.0,       # 最小ADX阈值
        max_adx=70.0        # 最大ADX阈值
    )
    
    print(f"原始交易数量: {len(filter_engine.original_df)}")
    print(f"过滤后数量: {len(filtered_df)}")
    
    if len(filtered_df) > 0:
        print(f"通过率: {len(filtered_df)/len(filter_engine.original_df)*100:.1f}%")
        print(f"平均ADX: {filtered_df['ADX'].mean():.2f}")
        
        # 保存结果
        output_file = "adx_filtered_basic.csv"
        filtered_df.to_csv(output_file, index=False)
        print(f"💾 结果已保存: {output_file}")

def example_conservative_adx_filter():
    """保守ADX过滤示例 - 只在强趋势中交易"""
    print("\n🛡️  保守ADX过滤示例")
    print("="*50)
    
    csv_file = "backtest_money_log_quick.csv"
    if not os.path.exists(csv_file):
        print(f"❌ 找不到回测结果文件: {csv_file}")
        return
    
    filter_engine = BacktestFilter(csv_file, "coin_data.db")
    
    # 加载价格数据
    filter_engine.load_price_volume_data('ETH', '5m', 'spot')
    
    # 保守设置：只在明确趋势中交易
    filtered_df = filter_engine.apply_adx_filter(
        adx_period=14,
        min_adx=30.0,       # 更高的最小阈值
        max_adx=65.0        # 更低的最大阈值
    )
    
    print(f"原始交易数量: {len(filter_engine.original_df)}")
    print(f"过滤后数量: {len(filtered_df)}")
    
    if len(filtered_df) > 0:
        print(f"通过率: {len(filtered_df)/len(filter_engine.original_df)*100:.1f}%")
        print(f"平均ADX: {filtered_df['ADX'].mean():.2f}")
        
        output_file = "adx_filtered_conservative.csv"
        filtered_df.to_csv(output_file, index=False)
        print(f"💾 结果已保存: {output_file}")

def example_comprehensive_filter_with_adx():
    """综合过滤示例 - ADX + 其他过滤器"""
    print("\n🎯 综合过滤示例 (ADX + 置信度 + 风控)")
    print("="*50)
    
    csv_file = "backtest_money_log_quick.csv"
    if not os.path.exists(csv_file):
        print(f"❌ 找不到回测结果文件: {csv_file}")
        return
    
    # 创建综合过滤配置
    config = {
        "coin": "ETH",
        "interval": "5m",
        "market": "spot",
        "initial_capital": 1000,
        "confidence_filter": {
            "enabled": True,
            "min_confidence": 0.65,
            "max_confidence": 1.0
        },
        "adx_filter": {
            "enabled": True,
            "adx_period": 14,
            "min_adx": 25.0,
            "max_adx": 70.0
        },
        "consecutive_filter": {
            "enabled": True,
            "max_consecutive_losses": 3
        }
    }
    
    filter_engine = BacktestFilter(csv_file, "coin_data.db")
    
    # 计算原始指标
    original_df_with_capital = filter_engine.recalculate_capital_and_metrics(
        filter_engine.original_df.copy(), config['initial_capital']
    )
    original_metrics = filter_engine.calculate_performance_metrics(
        original_df_with_capital, config['initial_capital']
    )
    
    # 运行综合过滤
    filtered_df, filtered_metrics = filter_engine.run_comprehensive_filter(config)
    
    # 打印对比报告
    filter_engine.print_comparison_report(original_metrics, filtered_metrics)
    
    # 保存结果
    output_file = "adx_filtered_comprehensive.csv"
    filtered_df.to_csv(output_file, index=False)
    print(f"💾 综合过滤结果已保存: {output_file}")
    
    # 保存配置
    config_file = "adx_comprehensive_config.json"
    with open(config_file, 'w', encoding='utf-8') as f:
        json.dump(config, f, indent=2, ensure_ascii=False)
    print(f"⚙️  配置已保存: {config_file}")

def example_parameter_comparison():
    """ADX参数对比示例"""
    print("\n📊 ADX参数对比示例")
    print("="*50)
    
    csv_file = "backtest_money_log_quick.csv"
    if not os.path.exists(csv_file):
        print(f"❌ 找不到回测结果文件: {csv_file}")
        return
    
    filter_engine = BacktestFilter(csv_file, "coin_data.db")
    
    # 测试不同的ADX参数组合
    test_configs = [
        {"name": "宽松", "min_adx": 20.0, "max_adx": 75.0},
        {"name": "标准", "min_adx": 25.0, "max_adx": 70.0},
        {"name": "保守", "min_adx": 30.0, "max_adx": 65.0},
        {"name": "严格", "min_adx": 35.0, "max_adx": 60.0},
    ]
    
    results = []
    
    for config in test_configs:
        # 重新加载原始数据
        filter_engine.original_df = pd.read_csv(csv_file)
        filter_engine.original_df['StartTimestamp'] = pd.to_datetime(
            filter_engine.original_df['StartTimestamp'].str.replace(' UTC', ''), utc=True
        )
        filter_engine.original_df['EndTimestamp'] = pd.to_datetime(
            filter_engine.original_df['EndTimestamp'].str.replace(' UTC', ''), utc=True
        )
        
        # 确保价格数据已加载
        if filter_engine.price_data is None:
            filter_engine.load_price_volume_data('ETH', '5m', 'spot')
        
        # 应用ADX过滤
        filtered_df = filter_engine.apply_adx_filter(
            adx_period=14,
            min_adx=config["min_adx"],
            max_adx=config["max_adx"]
        )
        
        # 计算指标
        if len(filtered_df) > 0:
            win_rate = len(filtered_df[filtered_df['ProfitLoss'] > 0]) / len(filtered_df) * 100
            avg_profit = filtered_df['ProfitLoss'].mean()
            total_profit = filtered_df['ProfitLoss'].sum()
        else:
            win_rate = 0
            avg_profit = 0
            total_profit = 0
        
        results.append({
            "策略": config["name"],
            "ADX范围": f"{config['min_adx']}-{config['max_adx']}",
            "信号数量": len(filtered_df),
            "通过率": f"{len(filtered_df)/len(filter_engine.original_df)*100:.1f}%",
            "胜率": f"{win_rate:.1f}%",
            "平均收益": f"{avg_profit:.2f}",
            "总收益": f"{total_profit:.2f}"
        })
    
    # 打印对比表格
    print(f"\n{'策略':<8} {'ADX范围':<12} {'信号数量':<8} {'通过率':<8} {'胜率':<8} {'平均收益':<10} {'总收益':<10}")
    print("-" * 80)
    for result in results:
        print(f"{result['策略']:<8} {result['ADX范围']:<12} {result['信号数量']:<8} "
              f"{result['通过率']:<8} {result['胜率']:<8} {result['平均收益']:<10} {result['总收益']:<10}")

def example_adx_analysis():
    """ADX数据分析示例"""
    print("\n📈 ADX数据分析示例")
    print("="*50)
    
    csv_file = "backtest_money_log_quick.csv"
    if not os.path.exists(csv_file):
        print(f"❌ 找不到回测结果文件: {csv_file}")
        return
    
    filter_engine = BacktestFilter(csv_file, "coin_data.db")
    
    # 加载价格数据并计算ADX
    filter_engine.load_price_volume_data('ETH', '5m', 'spot')
    adx_data = filter_engine.calculate_adx_indicators(14)
    
    if adx_data is not None:
        print("ADX统计信息:")
        print(adx_data.describe())
        
        # 分析ADX分布
        adx_ranges = [
            ("震荡市场", 0, 25),
            ("弱趋势", 25, 40),
            ("中等趋势", 40, 60),
            ("强趋势", 60, 80),
            ("极强趋势", 80, 100)
        ]
        
        print(f"\nADX分布分析:")
        print(f"{'市场状态':<12} {'ADX范围':<12} {'数据点数':<10} {'占比':<8}")
        print("-" * 50)
        
        for name, min_val, max_val in adx_ranges:
            mask = (adx_data['adx'] >= min_val) & (adx_data['adx'] < max_val)
            count = mask.sum()
            percentage = count / len(adx_data) * 100
            print(f"{name:<12} {min_val}-{max_val:<9} {count:<10} {percentage:.1f}%")
        
        # 保存ADX数据
        adx_output = "adx_analysis_data.csv"
        adx_data.to_csv(adx_output)
        print(f"\n💾 ADX分析数据已保存: {adx_output}")

if __name__ == '__main__':
    print("🚀 ADX过滤器使用示例")
    print("="*60)
    
    try:
        # 基础ADX过滤
        example_basic_adx_filter()
        
        # 保守ADX过滤
        example_conservative_adx_filter()
        
        # 综合过滤
        example_comprehensive_filter_with_adx()
        
        # 参数对比
        example_parameter_comparison()
        
        # ADX数据分析
        example_adx_analysis()
        
        print("\n✅ 所有示例运行完成！")
        
    except Exception as e:
        print(f"\n❌ 运行过程中出现错误: {e}")
        import traceback
        traceback.print_exc()