#!/usr/bin/env python3
"""
测试ADX过滤功能
"""

import pandas as pd
import numpy as np
from backtest_after_filter import BacktestFilter
import matplotlib.pyplot as plt
import os

def create_test_data():
    """创建测试数据"""
    # 创建模拟的回测结果
    dates = pd.date_range('2024-01-01', periods=100, freq='5min', tz='UTC')
    
    # 预先生成累积资金序列
    profit_losses = np.random.normal(0, 10, len(dates))
    cumulative_capital = 1000 + np.cumsum(profit_losses)
    
    test_data = []
    for i, date in enumerate(dates):
        test_data.append({
            'StartTimestamp': date.strftime('%Y-%m-%d %H:%M:%S UTC'),
            'EndTimestamp': (date + pd.Timedelta(minutes=30)).strftime('%Y-%m-%d %H:%M:%S UTC'),
            'StartPrice': 2000 + np.random.normal(0, 50),
            'EndPrice': 2000 + np.random.normal(0, 50),
            'Prediction': np.random.choice([0, 1]),
            'Confidence': np.random.uniform(0.5, 0.9),
            'ProfitLoss': profit_losses[i],
            'CapitalAfter': cumulative_capital[i],
            'StartHour': date.hour,
            'StartDayName': date.strftime('%A')
        })
    
    df = pd.DataFrame(test_data)
    df.to_csv('test_backtest_results.csv', index=False)
    print("✅ 测试回测结果已创建: test_backtest_results.csv")
    
    # 创建模拟的价格数据
    price_dates = pd.date_range('2024-01-01', periods=200, freq='5min', tz='UTC')
    price_data = []
    
    base_price = 2000
    for date in price_dates:
        # 模拟价格走势，包含趋势和震荡
        trend = np.sin(len(price_data) * 0.1) * 100  # 长期趋势
        noise = np.random.normal(0, 20)  # 随机噪音
        
        open_price = base_price + trend + noise
        high_price = open_price + abs(np.random.normal(0, 15))
        low_price = open_price - abs(np.random.normal(0, 15))
        close_price = open_price + np.random.normal(0, 10)
        volume = np.random.uniform(1000, 5000)
        
        price_data.append({
            'timestamp': int(date.timestamp()),
            'open': open_price,
            'high': high_price,
            'low': low_price,
            'close': close_price,
            'volume': volume
        })
        
        base_price = close_price  # 下一个周期的基准价格
    
    # 保存到SQLite数据库
    import sqlite3
    conn = sqlite3.connect('test_coin_data.db')
    
    # 创建表 - 使用正确的表名格式
    table_name = "ETHUSDT_5min_spot"
    conn.execute(f'''
        CREATE TABLE IF NOT EXISTS {table_name} (
            timestamp INTEGER PRIMARY KEY,
            open REAL,
            high REAL,
            low REAL,
            close REAL,
            volume REAL
        )
    ''')
    
    # 插入数据
    for data in price_data:
        conn.execute(f'''
            INSERT OR REPLACE INTO {table_name} 
            (timestamp, open, high, low, close, volume) 
            VALUES (?, ?, ?, ?, ?, ?)
        ''', (data['timestamp'], data['open'], data['high'], 
              data['low'], data['close'], data['volume']))
    
    conn.commit()
    conn.close()
    print("✅ 测试价格数据已创建: test_coin_data.db")

def test_adx_calculation():
    """测试ADX计算"""
    print("\n🧪 测试ADX计算...")
    
    # 创建测试数据
    create_test_data()
    
    # 创建过滤器
    filter_engine = BacktestFilter('test_backtest_results.csv', 'test_coin_data.db')
    
    # 加载价格数据
    filter_engine.load_price_volume_data('ETH', '5m', 'spot')
    
    # 计算ADX指标
    adx_data = filter_engine.calculate_adx_indicators(14)
    
    if adx_data is not None:
        print(f"✅ ADX数据计算成功，共 {len(adx_data)} 条记录")
        print("\nADX统计信息:")
        print(adx_data.describe())
        
        # 绘制ADX图表
        plt.figure(figsize=(15, 10))
        
        # 子图1: 价格
        plt.subplot(3, 1, 1)
        price_data = filter_engine.price_data
        plt.plot(price_data.index, price_data['close'], label='Close Price', linewidth=1)
        plt.title('价格走势')
        plt.ylabel('价格')
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        # 子图2: ADX和DI
        plt.subplot(3, 1, 2)
        plt.plot(adx_data.index, adx_data['adx'], label='ADX', linewidth=2, color='purple')
        plt.plot(adx_data.index, adx_data['plus_di'], label='+DI', linewidth=1, color='green')
        plt.plot(adx_data.index, adx_data['minus_di'], label='-DI', linewidth=1, color='red')
        plt.axhline(y=25, color='orange', linestyle='--', alpha=0.7, label='ADX=25 (趋势阈值)')
        plt.axhline(y=70, color='red', linestyle='--', alpha=0.7, label='ADX=70 (过强阈值)')
        plt.title('ADX趋势强度指标')
        plt.ylabel('ADX值')
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        # 子图3: DX
        plt.subplot(3, 1, 3)
        plt.plot(adx_data.index, adx_data['dx'], label='DX', linewidth=1, color='blue')
        plt.title('方向性指标 (DX)')
        plt.ylabel('DX值')
        plt.xlabel('时间')
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig('test_adx_indicators.png', dpi=300, bbox_inches='tight')
        plt.show()
        print("📊 ADX指标图表已保存: test_adx_indicators.png")
        
    else:
        print("❌ ADX数据计算失败")

def test_adx_filter():
    """测试ADX过滤功能"""
    print("\n🧪 测试ADX过滤功能...")
    
    # 创建过滤器
    filter_engine = BacktestFilter('test_backtest_results.csv', 'test_coin_data.db')
    
    # 加载价格数据
    filter_engine.load_price_volume_data('ETH', '5m', 'spot')
    
    # 应用ADX过滤
    original_count = len(filter_engine.original_df)
    filtered_df = filter_engine.apply_adx_filter(
        adx_period=14,
        min_adx=25.0,
        max_adx=70.0
    )
    
    print(f"\n过滤结果:")
    print(f"原始交易数量: {original_count}")
    print(f"过滤后数量: {len(filtered_df)}")
    print(f"通过率: {len(filtered_df)/original_count*100:.1f}%")
    
    if len(filtered_df) > 0:
        print(f"\nADX统计 (过滤后):")
        if 'ADX' in filtered_df.columns:
            print(f"平均ADX: {filtered_df['ADX'].mean():.2f}")
            print(f"ADX范围: {filtered_df['ADX'].min():.2f} - {filtered_df['ADX'].max():.2f}")
        else:
            print("ADX列不存在 - 可能是因为价格数据加载失败")
        
        # 保存过滤结果
        filtered_df.to_csv('test_adx_filtered_results.csv', index=False)
        print("💾 过滤结果已保存: test_adx_filtered_results.csv")
    else:
        print("⚠️  没有交易通过ADX过滤")

def test_comprehensive_filter():
    """测试综合过滤功能"""
    print("\n🧪 测试综合过滤功能...")
    
    # 创建测试配置
    test_config = {
        "coin": "ETH",
        "interval": "5m",
        "market": "spot",
        "initial_capital": 1000,
        "confidence_filter": {
            "enabled": True,
            "min_confidence": 0.6,
            "max_confidence": 1.0
        },
        "adx_filter": {
            "enabled": True,
            "adx_period": 14,
            "min_adx": 25.0,
            "max_adx": 70.0
        },
        "consecutive_filter": {
            "enabled": True,
            "max_consecutive_losses": 2
        }
    }
    
    # 创建过滤器
    filter_engine = BacktestFilter('test_backtest_results.csv', 'test_coin_data.db')
    
    # 计算原始指标
    original_df_with_capital = filter_engine.recalculate_capital_and_metrics(
        filter_engine.original_df.copy(), test_config['initial_capital']
    )
    original_metrics = filter_engine.calculate_performance_metrics(
        original_df_with_capital, test_config['initial_capital']
    )
    
    # 运行综合过滤
    filtered_df, filtered_metrics = filter_engine.run_comprehensive_filter(test_config)
    
    # 打印对比报告
    filter_engine.print_comparison_report(original_metrics, filtered_metrics)

def cleanup_test_files():
    """清理测试文件"""
    test_files = [
        'test_backtest_results.csv',
        'test_coin_data.db',
        'test_adx_filtered_results.csv',
        'test_adx_indicators.png'
    ]
    
    for file in test_files:
        if os.path.exists(file):
            os.remove(file)
            print(f"🗑️  已删除测试文件: {file}")

if __name__ == '__main__':
    print("🚀 开始ADX过滤功能测试...")
    
    try:
        # 测试ADX计算
        test_adx_calculation()
        
        # 测试ADX过滤
        test_adx_filter()
        
        # 测试综合过滤
        test_comprehensive_filter()
        
        print("\n✅ 所有测试完成！")
        
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理测试文件
        input("\n按回车键清理测试文件...")
        cleanup_test_files()