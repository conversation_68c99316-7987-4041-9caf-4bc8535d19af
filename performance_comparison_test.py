#!/usr/bin/env python3
"""
性能对比测试脚本
对比原版和优化版过滤器的性能差异
"""

import time
import pandas as pd
import numpy as np
from backtest_after_filter import BacktestFilter
from backtest_after_filter_optimized import OptimizedBacktestFilter
import json

def create_performance_test_config():
    """创建性能测试配置"""
    return {
        "coin": "ETH",
        "interval": "5m", 
        "market": "spot",
        "initial_capital": 1000,
        "confidence_filter": {
            "enabled": True,
            "min_confidence": 0.6,
            "max_confidence": 1.0
        },
        "adx_filter": {
            "enabled": True,
            "adx_period": 14,
            "min_adx": 25.0,
            "max_adx": 70.0
        },
        "consecutive_filter": {
            "enabled": True,
            "max_consecutive_losses": 3
        }
    }

def test_original_filter(config):
    """测试原版过滤器性能"""
    print("🐌 测试原版过滤器...")
    
    start_time = time.time()
    
    try:
        filter_engine = BacktestFilter("backtest_money_log_quick.csv", "coin_data.db")
        
        # 计算原始指标
        original_df_with_capital = filter_engine.recalculate_capital_and_metrics(
            filter_engine.original_df.copy(), config['initial_capital']
        )
        original_metrics = filter_engine.calculate_performance_metrics(
            original_df_with_capital, config['initial_capital']
        )
        
        # 运行综合过滤
        filtered_df, filtered_metrics = filter_engine.run_comprehensive_filter(config)
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        return {
            'success': True,
            'execution_time': execution_time,
            'original_count': len(filter_engine.original_df),
            'filtered_count': len(filtered_df),
            'original_metrics': original_metrics,
            'filtered_metrics': filtered_metrics
        }
        
    except Exception as e:
        end_time = time.time()
        return {
            'success': False,
            'execution_time': end_time - start_time,
            'error': str(e)
        }

def test_optimized_filter(config):
    """测试优化版过滤器性能"""
    print("⚡ 测试优化版过滤器...")
    
    start_time = time.time()
    
    try:
        filter_engine = OptimizedBacktestFilter("backtest_money_log_quick.csv", "coin_data.db")
        
        # 计算原始指标
        original_df_with_capital = filter_engine.recalculate_capital_vectorized(
            filter_engine.original_df.copy(), config['initial_capital']
        )
        original_metrics = filter_engine.calculate_performance_metrics(
            original_df_with_capital, config['initial_capital']
        )
        
        # 运行优化版综合过滤
        filtered_df, filtered_metrics = filter_engine.run_comprehensive_filter_optimized(config)
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        return {
            'success': True,
            'execution_time': execution_time,
            'original_count': len(filter_engine.original_df),
            'filtered_count': len(filtered_df),
            'original_metrics': original_metrics,
            'filtered_metrics': filtered_metrics
        }
        
    except Exception as e:
        end_time = time.time()
        return {
            'success': False,
            'execution_time': end_time - start_time,
            'error': str(e)
        }

def compare_results(original_result, optimized_result):
    """对比两个版本的结果"""
    print("\n" + "="*80)
    print("📊 性能对比报告")
    print("="*80)
    
    if not original_result['success']:
        print(f"❌ 原版过滤器执行失败: {original_result['error']}")
        return
    
    if not optimized_result['success']:
        print(f"❌ 优化版过滤器执行失败: {optimized_result['error']}")
        return
    
    # 性能对比
    original_time = original_result['execution_time']
    optimized_time = optimized_result['execution_time']
    speedup = original_time / optimized_time if optimized_time > 0 else float('inf')
    
    print(f"\n⏱️  执行时间对比:")
    print(f"原版过滤器:   {original_time:.2f} 秒")
    print(f"优化版过滤器: {optimized_time:.2f} 秒")
    print(f"性能提升:     {speedup:.1f}x 倍")
    
    # 结果一致性检查
    print(f"\n📈 结果一致性检查:")
    print(f"原始信号数量: {original_result['original_count']} vs {optimized_result['original_count']}")
    print(f"过滤后数量:   {original_result['filtered_count']} vs {optimized_result['filtered_count']}")
    
    # 检查关键指标是否一致
    orig_metrics = original_result['filtered_metrics']
    opt_metrics = optimized_result['filtered_metrics']
    
    metrics_to_check = ['total_trades', 'win_rate', 'total_return_pct']
    
    print(f"\n📊 关键指标对比:")
    print(f"{'指标':<15} {'原版':<12} {'优化版':<12} {'差异':<10}")
    print("-" * 55)
    
    all_consistent = True
    for metric in metrics_to_check:
        orig_val = orig_metrics.get(metric, 0)
        opt_val = opt_metrics.get(metric, 0)
        diff = abs(orig_val - opt_val)
        
        if metric == 'total_trades':
            consistent = diff == 0
        else:
            consistent = diff < 0.01  # 允许小的浮点误差
        
        if not consistent:
            all_consistent = False
        
        status = "✅" if consistent else "❌"
        print(f"{metric:<15} {orig_val:<12.2f} {opt_val:<12.2f} {diff:<10.4f} {status}")
    
    if all_consistent:
        print(f"\n✅ 结果完全一致！优化版在保持准确性的同时提升了 {speedup:.1f}x 倍性能")
    else:
        print(f"\n⚠️  结果存在差异，需要进一步检查")
    
    # 内存使用估算
    print(f"\n💾 估算内存优化:")
    data_points = original_result['original_count']
    if data_points > 0:
        original_operations = data_points * 10  # 估算原版的操作数
        optimized_operations = data_points * 2  # 估算优化版的操作数
        memory_reduction = (original_operations - optimized_operations) / original_operations * 100
        print(f"估算内存操作减少: {memory_reduction:.1f}%")

def run_multiple_tests(num_tests=3):
    """运行多次测试取平均值"""
    print(f"🔄 运行 {num_tests} 次测试取平均值...")
    
    config = create_performance_test_config()
    
    original_times = []
    optimized_times = []
    
    for i in range(num_tests):
        print(f"\n--- 第 {i+1} 次测试 ---")
        
        # 测试原版（如果第一次测试成功的话）
        if i == 0 or len(original_times) == i:
            original_result = test_original_filter(config)
            if original_result['success']:
                original_times.append(original_result['execution_time'])
            else:
                print(f"❌ 原版测试失败: {original_result['error']}")
                break
        
        # 测试优化版
        optimized_result = test_optimized_filter(config)
        if optimized_result['success']:
            optimized_times.append(optimized_result['execution_time'])
        else:
            print(f"❌ 优化版测试失败: {optimized_result['error']}")
            break
    
    if original_times and optimized_times:
        avg_original = np.mean(original_times)
        avg_optimized = np.mean(optimized_times)
        avg_speedup = avg_original / avg_optimized
        
        print(f"\n📊 平均性能结果 ({len(original_times)} 次测试):")
        print(f"原版平均时间:   {avg_original:.2f} ± {np.std(original_times):.2f} 秒")
        print(f"优化版平均时间: {avg_optimized:.2f} ± {np.std(optimized_times):.2f} 秒")
        print(f"平均性能提升:   {avg_speedup:.1f}x 倍")
        
        return original_result, optimized_result
    
    return None, None

def main():
    """主函数"""
    print("🚀 回测过滤器性能对比测试")
    print("="*60)
    
    # 检查必要文件
    import os
    if not os.path.exists("backtest_money_log_quick.csv"):
        print("❌ 找不到回测结果文件: backtest_money_log_quick.csv")
        return
    
    if not os.path.exists("coin_data.db"):
        print("❌ 找不到数据库文件: coin_data.db")
        return
    
    try:
        # 运行单次对比测试
        config = create_performance_test_config()
        
        print("🧪 单次性能测试:")
        original_result = test_original_filter(config)
        optimized_result = test_optimized_filter(config)
        
        compare_results(original_result, optimized_result)
        
        # 运行多次测试（可选）
        print(f"\n" + "="*60)
        user_input = input("是否运行多次测试以获得更准确的平均值？(y/N): ")
        if user_input.lower() in ['y', 'yes']:
            run_multiple_tests(3)
        
        print(f"\n✅ 性能测试完成！")
        
        # 保存测试结果
        if original_result['success'] and optimized_result['success']:
            test_results = {
                'original_time': original_result['execution_time'],
                'optimized_time': optimized_result['execution_time'],
                'speedup': original_result['execution_time'] / optimized_result['execution_time'],
                'data_points': original_result['original_count'],
                'filtered_points': original_result['filtered_count']
            }
            
            with open('performance_test_results.json', 'w') as f:
                json.dump(test_results, f, indent=2)
            print(f"📊 测试结果已保存到: performance_test_results.json")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()