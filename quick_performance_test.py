#!/usr/bin/env python3
"""
快速性能测试 - 专门测试ADX过滤的性能差异
"""

import time
import pandas as pd
import numpy as np
from backtest_after_filter import BacktestFilter
from backtest_after_filter_optimized import OptimizedBacktestFilter

def test_adx_calculation_performance():
    """测试ADX计算性能"""
    print("🧪 ADX计算性能测试")
    print("="*50)
    
    # 测试原版ADX计算
    print("🐌 测试原版ADX计算...")
    original_filter = BacktestFilter("backtest_money_log_quick.csv", "coin_data.db")
    original_filter.load_price_volume_data('ETH', '5m', 'spot')
    
    start_time = time.time()
    original_adx = original_filter.calculate_adx_indicators(14)
    original_time = time.time() - start_time
    
    print(f"原版ADX计算时间: {original_time:.2f} 秒")
    print(f"ADX数据点数: {len(original_adx) if original_adx is not None else 0}")
    
    # 测试优化版ADX计算
    print("\n⚡ 测试优化版ADX计算...")
    optimized_filter = OptimizedBacktestFilter("backtest_money_log_quick.csv", "coin_data.db")
    optimized_filter.load_price_volume_data('ETH', '5m', 'spot')
    
    start_time = time.time()
    optimized_adx = optimized_filter.calculate_adx_indicators_vectorized(14)
    optimized_time = time.time() - start_time
    
    print(f"优化版ADX计算时间: {optimized_time:.2f} 秒")
    print(f"ADX数据点数: {len(optimized_adx) if optimized_adx is not None else 0}")
    
    # 性能对比
    if original_time > 0 and optimized_time > 0:
        speedup = original_time / optimized_time
        print(f"\n📊 ADX计算性能提升: {speedup:.1f}x 倍")
        
        # 验证结果一致性
        if original_adx is not None and optimized_adx is not None:
            adx_diff = (original_adx['adx'] - optimized_adx['adx']).abs().mean()
            print(f"ADX结果差异 (平均绝对误差): {adx_diff:.6f}")
            
            if adx_diff < 0.001:
                print("✅ 结果一致性验证通过")
            else:
                print("⚠️  结果存在差异")
    
    return original_time, optimized_time

def test_filter_application_performance():
    """测试过滤应用性能"""
    print("\n🧪 过滤应用性能测试")
    print("="*50)
    
    # 准备测试数据
    original_filter = BacktestFilter("backtest_money_log_quick.csv", "coin_data.db")
    original_filter.load_price_volume_data('ETH', '5m', 'spot')
    
    optimized_filter = OptimizedBacktestFilter("backtest_money_log_quick.csv", "coin_data.db")
    optimized_filter.load_price_volume_data('ETH', '5m', 'spot')
    
    # 测试原版过滤
    print("🐌 测试原版ADX过滤...")
    start_time = time.time()
    try:
        original_result = original_filter.apply_adx_filter(14, 25.0, 70.0)
        original_filter_time = time.time() - start_time
        original_count = len(original_result)
        print(f"原版过滤时间: {original_filter_time:.2f} 秒")
        print(f"过滤后信号数: {original_count}")
    except Exception as e:
        print(f"原版过滤失败: {e}")
        original_filter_time = float('inf')
        original_count = 0
    
    # 测试优化版过滤
    print("\n⚡ 测试优化版ADX过滤...")
    start_time = time.time()
    try:
        optimized_result = optimized_filter.apply_adx_filter_vectorized(14, 25.0, 70.0)
        optimized_filter_time = time.time() - start_time
        optimized_count = len(optimized_result)
        print(f"优化版过滤时间: {optimized_filter_time:.2f} 秒")
        print(f"过滤后信号数: {optimized_count}")
    except Exception as e:
        print(f"优化版过滤失败: {e}")
        optimized_filter_time = float('inf')
        optimized_count = 0
    
    # 性能对比
    if original_filter_time < float('inf') and optimized_filter_time < float('inf'):
        speedup = original_filter_time / optimized_filter_time
        print(f"\n📊 过滤应用性能提升: {speedup:.1f}x 倍")
        
        # 验证结果数量一致性
        count_diff = abs(original_count - optimized_count)
        print(f"结果数量差异: {count_diff}")
        
        if count_diff == 0:
            print("✅ 过滤结果数量一致")
        else:
            print("⚠️  过滤结果数量存在差异")
    
    return original_filter_time, optimized_filter_time

def main():
    """主函数"""
    print("🚀 快速性能测试")
    print("="*60)
    
    try:
        # 测试ADX计算性能
        adx_original_time, adx_optimized_time = test_adx_calculation_performance()
        
        # 测试过滤应用性能
        filter_original_time, filter_optimized_time = test_filter_application_performance()
        
        # 总结
        print(f"\n" + "="*60)
        print("📊 性能测试总结")
        print("="*60)
        
        print(f"ADX计算:")
        print(f"  原版: {adx_original_time:.2f} 秒")
        print(f"  优化版: {adx_optimized_time:.2f} 秒")
        if adx_optimized_time > 0:
            print(f"  提升: {adx_original_time/adx_optimized_time:.1f}x 倍")
        
        print(f"\n过滤应用:")
        print(f"  原版: {filter_original_time:.2f} 秒")
        print(f"  优化版: {filter_optimized_time:.2f} 秒")
        if filter_optimized_time > 0 and filter_optimized_time < float('inf'):
            print(f"  提升: {filter_original_time/filter_optimized_time:.1f}x 倍")
        
        # 保存结果
        results = {
            'adx_calculation': {
                'original_time': adx_original_time,
                'optimized_time': adx_optimized_time,
                'speedup': adx_original_time / adx_optimized_time if adx_optimized_time > 0 else 0
            },
            'filter_application': {
                'original_time': filter_original_time,
                'optimized_time': filter_optimized_time,
                'speedup': filter_original_time / filter_optimized_time if filter_optimized_time > 0 and filter_optimized_time < float('inf') else 0
            }
        }
        
        import json
        with open('quick_performance_results.json', 'w') as f:
            json.dump(results, f, indent=2)
        
        print(f"\n💾 测试结果已保存到: quick_performance_results.json")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()