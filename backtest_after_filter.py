# backtest_after_filter.py
# 对 backtest_money_quick.py 生成的详细CSV进行后处理过滤分析
# 支持多种过滤策略：双均线、成交量、时间段、连续信号等
# 重新计算资金收益和统计指标

import pandas as pd
import numpy as np
import argparse
import os
from datetime import datetime
import sqlite3
from typing import Dict, List, Optional, Tuple
import json
import bisect
# 引入分析工具
try:
    from analyze_money import analyze_and_plot_results
    from get_coin_history import get_table_name
except ImportError:
    print("警告: 无法导入 analyze_money 或 get_coin_history，部分功能可能受限")
    def analyze_and_plot_results(df, basename): 
        print(f"分析结果并绘图: {basename}")
    def get_table_name(coin, interval, market):
        return f"{coin.lower()}USDT_{interval}_{market}"

class BacktestFilter:
    """回测结果过滤器"""
    
    def __init__(self, csv_file: str, db_path: str = "coin_data.db"):
        self.csv_file = csv_file
        self.db_path = db_path
        self.original_df = None
        self.filtered_df = None
        self.price_data = None
        self.volume_data = None
        
        # 加载原始回测结果
        self.load_original_results()
        
    def load_original_results(self):
        """加载原始回测结果"""
        if not os.path.exists(self.csv_file):
            raise FileNotFoundError(f"回测结果文件不存在: {self.csv_file}")
        
        self.original_df = pd.read_csv(self.csv_file)
        print(f"✅ 已加载原始回测结果: {len(self.original_df)} 条记录")
        
        # 转换时间列 - 统一转换为带时区(UTC)的datetime对象
        # 这可以确保后续与从数据库加载的带时区价格数据进行比较时不会出错
        # pandas的to_datetime函数很强大，utc=True能正确处理多种输入格式
        self.original_df['StartTimestamp_formatted'] = self.original_df['StartTimestamp'].str.replace(' UTC', '')
        self.original_df['EndTimestamp_formatted'] = self.original_df['EndTimestamp'].str.replace(' UTC', '')
        self.original_df['StartTimestamp'] = pd.to_datetime(self.original_df['StartTimestamp_formatted'], utc=True)
        self.original_df['EndTimestamp'] = pd.to_datetime(self.original_df['EndTimestamp_formatted'], utc=True)
        
    def load_price_volume_data(self, coin: str, interval: str, market: str = "spot"):
        """加载价格和成交量数据用于过滤"""
        table_name = get_table_name(coin+"USDT", interval, market)
        try:
            conn = sqlite3.connect(self.db_path)
            query = f"""
            SELECT timestamp, open, high, low, close, volume 
            FROM {table_name} 
            ORDER BY timestamp ASC
            """
            df = pd.read_sql_query(query, conn)
            conn.close()
            
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='s', utc=True)
            df.set_index('timestamp', inplace=True)
            
            self.price_data = df[['open', 'high', 'low', 'close']].astype(float)
            self.volume_data = df['volume'].astype(float)
            
            print(f"✅ 已加载价格数据: {len(self.price_data)} 条记录")
            
        except Exception as e:
            print(f"❌ 加载价格数据失败: {e}")
            self.price_data = None
            self.volume_data = None
    
    def calculate_moving_averages(self, short_period: int = 5, long_period: int = 20) -> pd.DataFrame:
        """计算双均线指标"""
        if self.price_data is None:
            return None
            
        ma_data = pd.DataFrame(index=self.price_data.index)
        ma_data['close'] = self.price_data['close']
        ma_data[f'ma_{short_period}'] = self.price_data['close'].rolling(window=short_period).mean()
        ma_data[f'ma_{long_period}'] = self.price_data['close'].rolling(window=long_period).mean()
        
        # 计算均线信号：短均线在长均线上方为看涨(1)，下方为看跌(-1)
        ma_data['ma_signal'] = np.where(
            ma_data[f'ma_{short_period}'] > ma_data[f'ma_{long_period}'], 1, -1
        )
        
        return ma_data
    
    def calculate_volume_indicators(self, volume_period: int = 20) -> pd.DataFrame:
        """计算成交量指标"""
        if self.volume_data is None:
            return None
            
        vol_data = pd.DataFrame(index=self.volume_data.index)
        vol_data['volume'] = self.volume_data
        vol_data['volume_ma'] = self.volume_data.rolling(window=volume_period).mean()
        
        # 成交量相对强度：当前成交量 / 平均成交量
        vol_data['volume_ratio'] = vol_data['volume'] / vol_data['volume_ma']
        
        return vol_data
    
    def get_signal_at_time(self, timestamp: pd.Timestamp, signal_data: pd.DataFrame, 
                          signal_column: str) -> Optional[float]:
        """获取指定时间点的信号值"""
        if signal_data is None or signal_data.empty:
            return None
            
        # 找到最接近但不超过指定时间的数据点
        valid_data = signal_data[signal_data.index <= timestamp]
        if valid_data.empty:
            return None
            
        return valid_data.iloc[-1][signal_column]
    
    def apply_ma_filter(self, short_period: int = 5, long_period: int = 20, 
                       same_direction_only: bool = True) -> pd.DataFrame:
        """应用双均线过滤"""
        print(f"🔍 应用双均线过滤 (MA{short_period}/MA{long_period})")
        
        ma_data = self.calculate_moving_averages(short_period, long_period)
        if ma_data is None:
            print("❌ 无法计算均线数据，跳过均线过滤")
            return self.original_df.copy()
        
        filtered_df = self.original_df.copy()
        filtered_df['MA_Signal'] = np.nan
        filtered_df['MA_Filter_Pass'] = False
        
        for idx, row in filtered_df.iterrows():
            ma_signal = self.get_signal_at_time(row['StartTimestamp'], ma_data, 'ma_signal')
            filtered_df.loc[idx, 'MA_Signal'] = ma_signal
            
            if ma_signal is not None and same_direction_only:
                # 只有当预测方向与均线信号一致时才通过过滤
                prediction = row['Prediction']  # 1=看涨, 0=看跌
                if (prediction == 1 and ma_signal == 1) or (prediction == 0 and ma_signal == -1):
                    filtered_df.loc[idx, 'MA_Filter_Pass'] = True
            elif ma_signal is not None and not same_direction_only:
                # 不限制方向，只要有均线信号就通过
                filtered_df.loc[idx, 'MA_Filter_Pass'] = True
        
        # 只保留通过过滤的记录
        passed_count = filtered_df['MA_Filter_Pass'].sum()
        filtered_df = filtered_df[filtered_df['MA_Filter_Pass']].copy()
        
        print(f"✅ 双均线过滤完成: {len(self.original_df)} -> {len(filtered_df)} 条记录 "
              f"(通过率: {passed_count/len(self.original_df)*100:.1f}%)")
        
        return filtered_df
    
    def apply_volume_filter(self, volume_period: int = 20, min_volume_ratio: float = 1.2) -> pd.DataFrame:
        """应用成交量过滤"""
        print(f"🔍 应用成交量过滤 (最小成交量比率: {min_volume_ratio:.1f}x)")
        
        vol_data = self.calculate_volume_indicators(volume_period)
        if vol_data is None:
            print("❌ 无法计算成交量数据，跳过成交量过滤")
            return self.original_df.copy()
        
        filtered_df = self.original_df.copy()
        filtered_df['Volume_Ratio'] = np.nan
        filtered_df['Volume_Filter_Pass'] = False
        
        for idx, row in filtered_df.iterrows():
            volume_ratio = self.get_signal_at_time(row['StartTimestamp'], vol_data, 'volume_ratio')
            filtered_df.loc[idx, 'Volume_Ratio'] = volume_ratio
            
            if volume_ratio is not None and volume_ratio >= min_volume_ratio:
                filtered_df.loc[idx, 'Volume_Filter_Pass'] = True
        
        # 只保留通过过滤的记录
        passed_count = filtered_df['Volume_Filter_Pass'].sum()
        filtered_df = filtered_df[filtered_df['Volume_Filter_Pass']].copy()
        
        print(f"✅ 成交量过滤完成: {len(self.original_df)} -> {len(filtered_df)} 条记录 "
              f"(通过率: {passed_count/len(self.original_df)*100:.1f}%)")
        
        return filtered_df
    
    def apply_time_filter(self, allowed_hours: List[int] = None, 
                         allowed_days: List[str] = None) -> pd.DataFrame:
        """应用时间段过滤"""
        if allowed_hours is None and allowed_days is None:
            return self.original_df.copy()
        
        print(f"🔍 应用时间段过滤")
        if allowed_hours:
            print(f"  允许小时: {allowed_hours}")
        if allowed_days:
            print(f"  允许星期: {allowed_days}")
        
        filtered_df = self.original_df.copy()
        filtered_df['Time_Filter_Pass'] = True
        
        if allowed_hours:
            hour_mask = filtered_df['StartHour'].isin(allowed_hours)
            filtered_df['Time_Filter_Pass'] &= hour_mask
        
        if allowed_days:
            day_mask = filtered_df['StartDayName'].isin(allowed_days)
            filtered_df['Time_Filter_Pass'] &= day_mask
        
        # 只保留通过过滤的记录
        passed_count = filtered_df['Time_Filter_Pass'].sum()
        filtered_df = filtered_df[filtered_df['Time_Filter_Pass']].copy()
        
        print(f"✅ 时间段过滤完成: {len(self.original_df)} -> {len(filtered_df)} 条记录 "
              f"(通过率: {passed_count/len(self.original_df)*100:.1f}%)")
        
        return filtered_df
    
    def apply_confidence_filter(self, min_confidence: float = 0.6, 
                               max_confidence: float = 1.0) -> pd.DataFrame:
        """应用置信度过滤"""
        print(f"🔍 应用置信度过滤 ({min_confidence:.3f} - {max_confidence:.3f})")
        
        filtered_df = self.original_df.copy()
        confidence_mask = (filtered_df['Confidence'] >= min_confidence) & \
                         (filtered_df['Confidence'] <= max_confidence)
        
        passed_count = confidence_mask.sum()
        filtered_df = filtered_df[confidence_mask].copy()
        
        print(f"✅ 置信度过滤完成: {len(self.original_df)} -> {len(filtered_df)} 条记录 "
              f"(通过率: {passed_count/len(self.original_df)*100:.1f}%)")
        
        return filtered_df
    def apply_open_loss_filter(self, max_open_losses: int = 2) -> pd.DataFrame:
        """
        应用持仓浮亏过滤。
        当准备开新仓时，检查当前所有持仓中处于浮动亏损状态的交易数量。
        如果浮亏的持仓数量达到或超过阈值，则放弃本次开仓机会。
        这是一种基于当前市场状态和组合风险的动态风控。
        """
        print(f"🔍 应用持仓浮亏过滤 (最大允许浮亏持仓数: {max_open_losses})")

        # 1. 前置检查：此过滤器必须有价格数据才能工作
        if self.price_data is None or self.price_data.empty:
            print("❌ 缺少价格数据，无法执行持仓浮亏过滤。跳过此过滤器。")
            return self.original_df.copy()

        # 准备一个用于快速价格查询的Series
        close_prices = self.price_data['close'].sort_index()

        # 2. 按开始时间对所有潜在交易进行排序
        potential_trades = self.original_df.copy().sort_values('StartTimestamp').reset_index()
        
        # 存储最终被接受的交易的原始DataFrame索引
        final_accepted_indices = []
        # 存储已接受并开仓的交易（完整的Series对象）
        open_trades = []

        # 3. 遍历每一个潜在的交易机会
        for _, trade_to_consider in potential_trades.iterrows():
            decision_time = trade_to_consider['StartTimestamp']
            
            # 在做决策前，先清理一下 open_trades 列表，移除已经平仓的
            open_trades = [t for t in open_trades if t['EndTimestamp'] > decision_time]
            # 4. 检查当前持仓的浮亏情况
            open_loss_count = 0
            # 获取决策时刻的最新市场价格
            # 使用 asof 可以找到该时间点或之前的最后一个有效价格
            current_price = close_prices.asof(decision_time)

            if pd.isna(current_price):
                # 如果找不到价格，为安全起见，可以跳过（或采取其他策略）
                continue

            for trade in open_trades:
                entry_price = trade['StartPrice']
                is_loss = False
                # 判断做多还是做空
                if trade['Prediction'] == 1: # 做多
                    if current_price < entry_price:
                        is_loss = True
                else: # 做空 (Prediction == 0)
                    if current_price > entry_price:
                        is_loss = True
                
                if is_loss:
                    open_loss_count += 1
            
            # 5. 做出决策
            if open_loss_count < max_open_losses:
                # 风险可控，接受这笔新交易
                final_accepted_indices.append(trade_to_consider['index'])
                open_trades.append(trade_to_consider)

        # 6. 根据最终接受的交易索引，从原始DataFrame中筛选结果
        if not final_accepted_indices:
            return pd.DataFrame(columns=self.original_df.columns)
            
        filtered_df = self.original_df.loc[final_accepted_indices].copy()

        original_len = len(self.original_df)
        passed_count = len(filtered_df)
        
        print(f"✅ 持仓浮亏过滤完成: {original_len} -> {passed_count} 条记录 "
            f"(通过率: {passed_count / original_len * 100:.1f}%)" if original_len > 0 else "✅ 持仓浮亏过滤完成: 0 -> 0 条记录")
        
        return filtered_df
    def apply_consecutive_filter(self, max_consecutive_losses: int = 3, cooldown_period_hours: int = 1) -> pd.DataFrame:
        """
        应用连续亏损过滤 (带冷静期的事件驱动最终版)。
        当连续亏损达到上限时，触发一个“冷静期”，在此期间暂停开新仓。
        冷静期结束后，策略自动恢复正常。这能有效防止策略永久锁死。
        """
        print(f"🔍 应用连续亏损过滤 (冷静期: {cooldown_period_hours}小时, 最大连续亏损: {max_consecutive_losses})")

        # 1. 确保数据有效
        if self.original_df.empty:
            return self.original_df.copy()

        # 2. 创建事件列表
        events = []
        for idx, row in self.original_df.iterrows():
            events.append({'timestamp': row['StartTimestamp'], 'type': 'start', 'trade_index': idx})
            events.append({'timestamp': row['EndTimestamp'], 'type': 'end', 'profit': row['ProfitLoss'], 'trade_index': idx})
            
        # 3. 按时间顺序对所有事件进行排序
        events.sort(key=lambda x: x['timestamp'])
        
        # 4. 初始化状态机
        consecutive_losses_counter = 0
        # 初始化冷静期结束时间为一个非常早的时间点，确保开始时不受影响
        # 必须设置为带时区，以避免与事件时间戳比较时出错
        cooldown_until = pd.Timestamp.min.tz_localize('UTC') 
        
        accepted_trade_indices = set()
        final_trade_indices = []

        # 5. 遍历事件流，模拟交易过程
        for event in events:
            timestamp = event['timestamp']
            
            if event['type'] == 'start':
                # 决策是否开仓
                # 必须同时满足两个条件：1.不在冷静期内 2.连续亏损计数未达上限
                if timestamp >= cooldown_until and consecutive_losses_counter < max_consecutive_losses:
                    # 状态允许，接受这笔交易
                    accepted_trade_indices.add(event['trade_index'])
                    final_trade_indices.append(event['trade_index'])
            
            elif event['type'] == 'end':
                # 平仓事件，用于更新状态
                if event['trade_index'] in accepted_trade_indices:
                    if event['profit'] < 0:
                        consecutive_losses_counter += 1
                        # 检查是否触发冷静期
                        if consecutive_losses_counter >= max_consecutive_losses:
                            print(f"  -> 触发冷静期! 时间: {timestamp}, 连续亏损: {consecutive_losses_counter}次。暂停交易至 {timestamp + pd.Timedelta(hours=cooldown_period_hours)}")
                            cooldown_until = timestamp + pd.Timedelta(hours=cooldown_period_hours)
                            # 【关键】触发冷静期后，重置计数器，因为惩罚已执行
                            consecutive_losses_counter = 0
                    else:
                        # 盈利，计数器清零
                        consecutive_losses_counter = 0

        # 6. 根据最终接受的交易索引，从原始DataFrame中筛选结果
        if not final_trade_indices:
            return pd.DataFrame(columns=self.original_df.columns)
            
        filtered_df = self.original_df.loc[final_trade_indices].sort_index().copy()

        original_len = len(self.original_df)
        passed_count = len(filtered_df)
        
        print(f"✅ 连续亏损过滤完成: {original_len} -> {passed_count} 条记录 "
            f"(通过率: {passed_count / original_len * 100:.1f}%)" if original_len > 0 else "✅ 连续亏损过滤完成: 0 -> 0 条记录")
        
        return filtered_df
    
    def recalculate_capital_and_metrics(self, filtered_df: pd.DataFrame, 
                                       initial_capital: float) -> pd.DataFrame:
        """重新计算资金序列和相关指标"""
        if filtered_df.empty:
            return filtered_df
        
        # 如果数据已经被过滤过，需要重新计算资金序列
        # 如果是原始数据，可以直接使用现有的 CapitalAfter 列
        if len(filtered_df) == len(self.original_df):
            # 这是原始数据，不需要重新计算
            print("💰 使用原始资金序列...")
            if 'CapitalAfter_Filtered' not in filtered_df.columns:
                filtered_df['CapitalAfter_Filtered'] = filtered_df['CapitalAfter']
        else:
            # 这是过滤后的数据，需要重新计算
            print("💰 重新计算资金序列和收益指标...")
            
            # 按时间排序
            filtered_df = filtered_df.sort_values('StartTimestamp').copy()
            
            # 重新计算累积资金
            current_capital = initial_capital
            capital_series = []
            
            for idx, row in filtered_df.iterrows():
                current_capital += row['ProfitLoss']
                capital_series.append(current_capital)
            
            filtered_df['CapitalAfter_Filtered'] = capital_series
        
        # 计算累积收益率
        filtered_df['CumulativeReturn'] = (filtered_df['CapitalAfter_Filtered'] - initial_capital) / initial_capital * 100
        
        return filtered_df
    
    def calculate_performance_metrics(self, filtered_df: pd.DataFrame, 
                                    initial_capital: float) -> Dict:
        """计算性能指标"""
        if filtered_df.empty:
            return {}
        
        # 检查使用哪个资金列
        capital_column = 'CapitalAfter_Filtered' if 'CapitalAfter_Filtered' in filtered_df.columns else 'CapitalAfter'
        
        final_capital = filtered_df[capital_column].iloc[-1]
        total_return = (final_capital - initial_capital) / initial_capital
        
        metrics = {
            'initial_capital': initial_capital,
            'final_capital': final_capital,
            'total_return': total_return,
            'total_return_pct': total_return * 100,
            'total_trades': len(filtered_df),
            'winning_trades': len(filtered_df[filtered_df['ProfitLoss'] > 0]),
            'losing_trades': len(filtered_df[filtered_df['ProfitLoss'] < 0]),
            'win_rate': len(filtered_df[filtered_df['ProfitLoss'] > 0]) / len(filtered_df) * 100,
        }
        
        # 计算最大回撤
        capital_series = filtered_df[capital_column].values
        rolling_max = np.maximum.accumulate(capital_series)
        drawdown = (capital_series - rolling_max) / rolling_max
        metrics['max_drawdown_pct'] = np.min(drawdown) * 100
        
        # 计算盈亏比
        winning_trades = filtered_df[filtered_df['ProfitLoss'] > 0]['ProfitLoss']
        losing_trades = filtered_df[filtered_df['ProfitLoss'] < 0]['ProfitLoss']
        
        if len(winning_trades) > 0 and len(losing_trades) > 0:
            metrics['avg_win'] = winning_trades.mean()
            metrics['avg_loss'] = losing_trades.mean()
            metrics['profit_factor'] = abs(winning_trades.sum() / losing_trades.sum())
        else:
            metrics['avg_win'] = winning_trades.mean() if len(winning_trades) > 0 else 0
            metrics['avg_loss'] = losing_trades.mean() if len(losing_trades) > 0 else 0
            metrics['profit_factor'] = float('inf') if len(losing_trades) == 0 else 0
        
        # 计算夏普比率
        if len(filtered_df) > 1:
            returns = filtered_df['ProfitLoss'] / initial_capital  # 简化的收益率计算
            if returns.std() > 0:
                metrics['sharpe_ratio'] = returns.mean() / returns.std() * np.sqrt(len(returns))
            else:
                metrics['sharpe_ratio'] = 0
        else:
            metrics['sharpe_ratio'] = 0
        
        return metrics
    
    def print_comparison_report(self, original_metrics: Dict, filtered_metrics: Dict):
        """打印对比报告"""
        print("\n" + "="*60)
        print("📊 过滤前后对比报告")
        print("="*60)
        
        print(f"\n{'指标':<20} {'过滤前':<15} {'过滤后':<15} {'变化':<15}")
        print("-" * 65)
        
        # 交易数量
        orig_trades = original_metrics.get('total_trades', 0)
        filt_trades = filtered_metrics.get('total_trades', 0)
        change_trades = f"{filt_trades-orig_trades:+d}" if orig_trades > 0 else "N/A"
        print(f"{'交易数量':<20} {orig_trades:<15} {filt_trades:<15} {change_trades:<15}")
        
        # 总收益率
        orig_return = original_metrics.get('total_return_pct', 0)
        filt_return = filtered_metrics.get('total_return_pct', 0)
        change_return = f"{filt_return-orig_return:+.2f}%" if orig_return != 0 else "N/A"
        print(f"{'总收益率':<20} {orig_return:.2f}%{'':<8} {filt_return:.2f}%{'':<8} {change_return:<15}")
        
        # 胜率
        orig_winrate = original_metrics.get('win_rate', 0)
        filt_winrate = filtered_metrics.get('win_rate', 0)
        change_winrate = f"{filt_winrate-orig_winrate:+.2f}%" if orig_winrate != 0 else "N/A"
        print(f"{'胜率':<20} {orig_winrate:.2f}%{'':<8} {filt_winrate:.2f}%{'':<8} {change_winrate:<15}")
        
        # 最大回撤
        orig_dd = original_metrics.get('max_drawdown_pct', 0)
        filt_dd = filtered_metrics.get('max_drawdown_pct', 0)
        change_dd = f"{filt_dd-orig_dd:+.2f}%" if orig_dd != 0 else "N/A"
        print(f"{'最大回撤':<20} {orig_dd:.2f}%{'':<8} {filt_dd:.2f}%{'':<8} {change_dd:<15}")
        
        # 盈亏比
        orig_pf = original_metrics.get('profit_factor', 0)
        filt_pf = filtered_metrics.get('profit_factor', 0)
        orig_pf_str = f"{orig_pf:.2f}" if orig_pf != float('inf') else "∞"
        filt_pf_str = f"{filt_pf:.2f}" if filt_pf != float('inf') else "∞"
        print(f"{'盈亏比':<20} {orig_pf_str:<15} {filt_pf_str:<15} {'N/A':<15}")
        
        # 夏普比率
        orig_sharpe = original_metrics.get('sharpe_ratio', 0)
        filt_sharpe = filtered_metrics.get('sharpe_ratio', 0)
        change_sharpe = f"{filt_sharpe-orig_sharpe:+.3f}" if orig_sharpe != 0 else "N/A"
        print(f"{'夏普比率':<20} {orig_sharpe:.3f}{'':<10} {filt_sharpe:.3f}{'':<10} {change_sharpe:<15}")
        
        print("\n" + "="*60)
    
    def run_comprehensive_filter(self, config: Dict) -> Tuple[pd.DataFrame, Dict]:
        """运行综合过滤策略"""
        print(f"\n🚀 开始综合过滤分析...")
        
        # 加载价格和成交量数据
        if 'coin' in config and 'interval' in config:
            self.load_price_volume_data(
                config['coin'], 
                config['interval'], 
                config.get('market', 'spot')
            )
        
        # 应用各种过滤器
        current_df = self.original_df.copy()
        
        # 1. 置信度过滤
        if config.get('confidence_filter', {}).get('enabled', False):
            conf_config = config['confidence_filter']
            self.original_df = self.apply_confidence_filter(
                min_confidence=conf_config.get('min_confidence', 0.6),
                max_confidence=conf_config.get('max_confidence', 1.0)
            )
        # 2. 双均线过滤
        # if config.get('ma_filter', {}).get('enabled', False):
        #     ma_config = config['ma_filter']
        #     current_df = self.apply_ma_filter(
        #         short_period=ma_config.get('short_period', 5),
        #         long_period=ma_config.get('long_period', 20),
        #         same_direction_only=ma_config.get('same_direction_only', True)
        #     )
        #     self.original_df = current_df
        
        # 3. 成交量过滤
        # if config.get('volume_filter', {}).get('enabled', False):
        #     vol_config = config['volume_filter']
        #     current_df = self.apply_volume_filter(
        #         volume_period=vol_config.get('volume_period', 20),
        #         min_volume_ratio=vol_config.get('min_volume_ratio', 1.2)
        #     )
        
        # 4. 时间段过滤
        # if config.get('time_filter', {}).get('enabled', False):
        #     time_config = config['time_filter']
        #     current_df = self.apply_time_filter(
        #         allowed_hours=time_config.get('allowed_hours'),
        #         allowed_days=time_config.get('allowed_days')
        #     )
        
        if config.get('consecutive_filter', {}).get('enabled', False):
            cons_config = config['consecutive_filter']
            # current_df = self.apply_consecutive_filter(
            #     max_consecutive_losses=cons_config.get('max_consecutive_losses', 3)
            # )
            current_df = self.apply_open_loss_filter(
                max_open_losses=cons_config.get('max_consecutive_losses', 3)
            )
        
        # 重新计算资金和指标
        initial_capital = config.get('initial_capital', 1000)
        current_df = self.recalculate_capital_and_metrics(current_df, initial_capital)
        
        # 计算性能指标
        filtered_metrics = self.calculate_performance_metrics(current_df, initial_capital)
        
        self.filtered_df = current_df
        return current_df, filtered_metrics


def load_filter_config(config_file: str) -> Dict:
    """加载过滤配置文件"""
    if os.path.exists(config_file):
        with open(config_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    else:
        # 返回默认配置
        return {
            "coin": "ETH",
            "interval": "15m",
            "market": "spot",
            "initial_capital": 1000,
            "confidence_filter": {
                "enabled": True,
                "min_confidence": 0.65,
                "max_confidence": 1.0
            },
            "ma_filter": {
                "enabled": True,
                "short_period": 5,
                "long_period": 20,
                "same_direction_only": True
            },
            "volume_filter": {
                "enabled": True,
                "volume_period": 20,
                "min_volume_ratio": 1.5
            },
            "time_filter": {
                "enabled": False,
                "allowed_hours": [9, 10, 11, 14, 15, 16],
                "allowed_days": ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday"]
            },
            "consecutive_filter": {
                "enabled": True,
                "max_consecutive_losses": 3
            }
        }


def main():
    parser = argparse.ArgumentParser(description="对回测结果进行后处理过滤分析")
    parser.add_argument("--csv", default="backtest_money_log_quick.csv", help="回测结果CSV文件路径")
    parser.add_argument("--db", default="coin_data.db", help="SQLite数据库路径")
    parser.add_argument("--config", default="filter_config.json", help="过滤配置文件路径")
    parser.add_argument("--output", help="输出文件名前缀 (默认基于输入文件名)")
    
    # 快速过滤选项
    parser.add_argument("--min-confidence", type=float, help="最小置信度阈值")
    parser.add_argument("--ma-short", type=int, default=5, help="短期均线周期")
    parser.add_argument("--ma-long", type=int, default=20, help="长期均线周期")
    parser.add_argument("--min-volume-ratio", type=float, default=1.5, help="最小成交量比率")
    parser.add_argument("--max-consecutive-losses", type=int, default=3, help="最大连续亏损次数")
    
    args = parser.parse_args()
    
    # 加载配置
    config = load_filter_config(args.config)
    
    # 命令行参数覆盖配置文件
    if args.min_confidence:
        config['confidence_filter']['min_confidence'] = args.min_confidence
        config['confidence_filter']['enabled'] = True
    
    if args.ma_short or args.ma_long:
        config['ma_filter']['short_period'] = args.ma_short
        config['ma_filter']['long_period'] = args.ma_long
        config['ma_filter']['enabled'] = False
    
    if args.min_volume_ratio:
        config['volume_filter']['min_volume_ratio'] = args.min_volume_ratio
        config['volume_filter']['enabled'] = False
    
    if args.max_consecutive_losses:
        config['consecutive_filter']['max_consecutive_losses'] = args.max_consecutive_losses
        config['consecutive_filter']['enabled'] = True
    
    # 创建过滤器
    filter_engine = BacktestFilter(args.csv, args.db)
    
    # 计算原始指标 - 先准备原始数据
    original_df_with_capital = filter_engine.recalculate_capital_and_metrics(
        filter_engine.original_df.copy(), config['initial_capital']
    )
    original_metrics = filter_engine.calculate_performance_metrics(
        original_df_with_capital, config['initial_capital']
    )
    
    # 运行综合过滤
    filtered_df, filtered_metrics = filter_engine.run_comprehensive_filter(config)
    
    # 打印对比报告
    filter_engine.print_comparison_report(original_metrics, filtered_metrics)
    
    # 保存结果
    if args.output:
        output_prefix = args.output
    else:
        output_prefix = os.path.splitext(args.csv)[0] + "_filtered"
    
    # 保存过滤后的详细结果
    filtered_csv = f"{output_prefix}.csv"
    filtered_df.to_csv(filtered_csv, index=False, float_format='%.4f')
    print(f"\n💾 过滤后结果已保存到: {filtered_csv}")
    
    # 保存性能指标对比
    metrics_csv = f"{output_prefix}_metrics_comparison.csv"
    comparison_df = pd.DataFrame([
        {**{f"original_{k}": v for k, v in original_metrics.items()},
         **{f"filtered_{k}": v for k, v in filtered_metrics.items()}}
    ])
    comparison_df.to_csv(metrics_csv, index=False, float_format='%.6f')
    print(f"📈 性能对比已保存到: {metrics_csv}")
    
    # 保存使用的配置
    config_output = f"{output_prefix}_config.json"
    with open(config_output, 'w', encoding='utf-8') as f:
        json.dump(config, f, indent=2, ensure_ascii=False)
    print(f"⚙️  过滤配置已保存到: {config_output}")
    
    # 生成分析图表
    if len(filtered_df) > 0:
        try:
            analyze_and_plot_results(filtered_df, output_prefix)
            print(f"📊 分析图表已生成")
        except Exception as e:
            print(f"⚠️  生成分析图表时出错: {e}")
    
    print(f"\n✅ 过滤分析完成！")


if __name__ == '__main__':
    main()